{"name": "partner-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "pretty-all": "prettier --write .", "test": "vitest --ui --coverage", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "cy:open": "cypress open", "cy:run": "cypress run"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@react-three/fiber": "^9.2.0", "@tabler/icons-react": "^3.34.0", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@tanstack/react-table": "^8.21.3", "autoprefixer": "^10.4.20", "aws-amplify": "^6.15.2", "axios": "^1.10.0", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "crypto-browserify": "^3.12.1", "embla-carousel-react": "8.5.1", "formik": "^2.4.6", "input-otp": "^1.4.2", "ioredis": "^5.6.1", "jose": "^6.0.11", "lucide-react": "^0.454.0", "motion": "^12.23.0", "next": "15.2.4", "next-themes": "^0.4.6", "papaparse": "^5.5.3", "process": "^0.11.10", "react": "^19", "react-dom": "^19", "react-hook-form": "^7.60.0", "recharts": "^2.15.4", "sonner": "^1.7.4", "stream-browserify": "^3.0.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.178.0", "util": "^0.12.5", "uuid": "^11.1.0", "vaul": "^1.1.2", "yup": "^1.6.1", "zod": "^3.25.75"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/ioredis": "^4.28.10", "@types/node": "^22", "@types/papaparse": "^5.3.16", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "cypress": "^14.5.1", "eslint": "9.30.1", "eslint-config-next": "15.3.5", "jsdom": "^26.1.0", "postcss": "^8", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "typescript": "^5", "vitest": "^3.2.4"}}