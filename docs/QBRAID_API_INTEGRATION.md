# QBraid API Integration Fix

## Issues Fixed

### 1. **Duplicate `/api` Prefix Issue**

**Problem**: URLs were being constructed as `http://localhost:3001/api/api/orgs/get/0/1`

- Base URL: `https://api.qbraid.com/api`
- Call: `externalClient.get('/api/orgs/get/...')`
- Result: `https://api.qbraid.com/api/api/orgs/get/...` ❌

**Solution**: Remove `/api` prefix from individual calls

- Base URL: `https://api.qbraid.com/api`
- Call: `externalClient.get('/orgs/get/...')`
- Result: `https://api.qbraid.com/api/orgs/get/...` ✅

### 2. **Localhost Default Issue**

**Problem**: When no environment variables set, it defaulted to `http://localhost:3001/api`

**Solution**: Changed default to QBraid staging API

- **Before**: `process.env.QBRAID_API_URL || 'http://localhost:3001/api'`
- **After**: `process.env.QBRAID_API_URL || 'https://api-staging-1.qbraid.com/api'`

## Environment Configuration

### Production

```bash
NODE_ENV=production
# Uses: https://api.qbraid.com/api
```

### Staging

```bash
NEXT_PUBLIC_NODE_ENV=staging
# Uses: https://api-staging-1.qbraid.com/api
```

### Development

```bash
NODE_ENV=development
QBRAID_API_URL=https://api-staging-1.qbraid.com/api  # Optional: Override default
# Falls back to: https://api-staging-1.qbraid.com/api
```

### Custom API URL

```bash
QBRAID_API_URL=https://your-custom-api.com/api
```

## Fixed API Calls

### 1. Organization List Hook

**File**: `hooks/use-api.ts`

```typescript
// Before: externalClient.get('/api/orgs/get/${page}/${limit}')  ❌
// After:  externalClient.get('/orgs/get/${page}/${limit}')      ✅
```

### 2. Organizations API Route

**File**: `app/api/orgs/get/[page]/[limit]/route.ts`

```typescript
// Before: externalClient.get('/api/orgs/get/${page}/${limit}')  ❌
// After:  externalClient.get('/orgs/get/${page}/${limit}')      ✅
```

### 3. External Role Fetching

**File**: `lib/external-roles.ts`

```typescript
// Already correct: externalClient.get('/orgs/get/0/50')         ✅
```

## Expected API Calls (After Fix)

### Development/Staging

```
GET https://api-staging-1.qbraid.com/api/orgs/get/0/10
GET https://api-staging-1.qbraid.com/api/orgs/users/{orgId}/0/100
```

### Production

```
GET https://api.qbraid.com/api/orgs/get/0/10
GET https://api.qbraid.com/api/orgs/users/{orgId}/0/100
```

## Authentication Headers (Unchanged)

All calls include proper authentication following the auth wall guide:

```typescript
headers: {
  'id-token': 'user-id-token',     // Primary
  'email': '<EMAIL>',     // Required/Optional
  'domain': 'qbraid',              // Optional
  'Content-Type': 'application/json'
}
```

## Verification

To verify the fix is working:

1. **Check Network Tab**: URLs should be `https://api.qbraid.com/api/*` or `https://api-staging-1.qbraid.com/api/*`
2. **Check Console**: Should see logs like `🔄 [External API] GET /orgs/get/0/10`
3. **No Duplicate `/api`**: Should NOT see `/api/api/` in any URLs

## Environment Variables Summary

```bash
# Production (automatically detected)
NODE_ENV=production

# Staging (explicitly set)
NEXT_PUBLIC_NODE_ENV=staging

# Development with custom API (optional)
QBRAID_API_URL=https://your-api-url.com/api

# Authentication (required for QBraid API)
QBRAID_API_TOKEN=your-api-token
QBRAID_API_EMAIL=your-api-email
```

The system will now correctly call the QBraid API without duplicate `/api` prefixes or localhost defaults!
