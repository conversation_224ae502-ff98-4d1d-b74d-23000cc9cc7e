# Enhanced Role Caching System

## Overview

The enhanced role caching system implements a granular `[emailId, orgId, role]` structure using TanStack Query to provide dynamic organization switching with optimized performance and real-time role updates.

## 🎯 Problem Statement

### Previous Limitations:
1. **Monolithic Role Caching**: Roles cached per user, not per organization
2. **Slow Organization Switching**: Required full API calls when switching orgs
3. **No Granular Updates**: Role changes required full cache invalidation
4. **Poor Multi-User Support**: Couldn't cache roles for different users efficiently

### Solution: Granular Role Caching
- **Structure**: `[emailId, orgId, role]` format
- **Dynamic Fetching**: Automatic role loading when switching organizations
- **Real-time Updates**: Granular cache updates without full refresh
- **Multi-User Support**: Cache roles for different users simultaneously

## 🏗️ Architecture

### Cache Structure

```typescript
interface UserOrgRole {
  emailId: string;
  orgId: string;
  role: string;
  orgName: string;
  updated: string;
}

interface EnhancedRoleCache {
  [key: string]: UserOrgRole; // key format: "email:orgId"
}
```

### Query Keys

```typescript
export const permissionKeys = {
  // Enhanced role caching keys
  userOrgRole: (email: string, orgId: string) => ['userOrgRole', email, orgId],
  userAllOrgRoles: (email: string) => ['userOrgRoles', email],
  orgRoles: (orgId: string) => ['orgRoles', orgId],
};
```

## 🚀 New Hooks and Functions

### 1. useUserOrgRole
```typescript
const { data: userRole, isLoading, error } = useUserOrgRole(email, orgId);
```
- Fetches role for specific user in specific organization
- Cached with key: `['userOrgRole', email, orgId]`
- 5-minute stale time, 15-minute garbage collection

### 2. useUserAllOrgRoles
```typescript
const { data: allRoles, isLoading } = useUserAllOrgRoles(email);
```
- Fetches roles for user across all organizations
- Parallel API calls for better performance
- Returns `EnhancedRoleCache` structure

### 3. useEnhancedOrgRole
```typescript
const {
  role,
  organization,
  isLoading,
  switchToOrganization,
  invalidateOrgRole,
  updateRoleInCache,
  isAdmin,
  canManage,
} = useEnhancedOrgRole(email, orgId);
```
- Complete organization-aware role management
- Prefetching for organization switching
- Cache management functions
- Convenience role checking methods

## 📡 Enhanced API Endpoints

### Updated `/api/user/roles` Route

**New Parameters:**
- `email`: Target user email (optional, defaults to session email)
- `orgId`: Organization ID (required)

**Enhanced Response:**
```json
{
  "success": true,
  "data": {
    "email": "<EMAIL>",
    "orgId": "org123",
    "role": "admin",
    "orgName": "Example Organization",
    "enhanced": true,
    "requestedEmail": "<EMAIL>",
    "sessionEmail": "<EMAIL>"
  }
}
```

## 🔄 Organization Switching Flow

### 1. User Initiates Switch
```typescript
await switchOrganization(newOrgId);
```

### 2. Enhanced Context Provider
```typescript
const switchOrganization = async (orgId: string) => {
  // 1. Prefetch role data for new organization
  const targetOrg = organizations.find(org => org.orgId === orgId);
  
  // 2. Update localStorage and context
  setCurrentOrgContext(orgId);
  setCurrentOrgId(orgId);
  
  // 3. Broadcast change event
  window.dispatchEvent(new CustomEvent('org-context-changed', {
    detail: { orgId, org: targetOrg }
  }));
};
```

### 3. Automatic Role Fetching
- TanStack Query automatically fetches role for new organization
- Uses cache key: `['userOrgRole', email, newOrgId]`
- Background loading with cached fallback

## 💾 Cache Management

### Prefetching
```typescript
// Prefetch role for organization switching
await queryClient.prefetchQuery({
  queryKey: permissionKeys.userOrgRole(email, orgId),
  queryFn: () => fetchUserOrgRole(email, orgId),
  staleTime: 5 * 60 * 1000,
});
```

### Cache Invalidation
```typescript
// Invalidate specific user-org role
queryClient.invalidateQueries({
  queryKey: permissionKeys.userOrgRole(email, orgId),
});
```

### Real-time Updates
```typescript
// Update role in cache without API call
const updatedRole: UserOrgRole = {
  emailId: email,
  orgId: orgId,
  role: newRole,
  orgName: orgName,
  updated: new Date().toISOString(),
};

queryClient.setQueryData(
  permissionKeys.userOrgRole(email, orgId),
  updatedRole
);
```

## 🧪 Testing and Demo

### Enhanced Role Cache Demo Component
- **Location**: `/rbac-demo` page
- **Features**:
  - Compare current vs enhanced systems
  - Test different email/org combinations
  - Cache management controls
  - Real-time cache key visualization

### Test Scenarios
1. **Organization Switching**: Switch between orgs and observe cached roles
2. **Multi-User Testing**: Test roles for different email addresses
3. **Cache Management**: Prefetch, invalidate, and update cache entries
4. **Performance**: Compare loading times with/without cache

## 📊 Performance Benefits

### Before (Monolithic Caching)
- ❌ Full API call on every org switch
- ❌ Cache invalidation affects all organizations
- ❌ No prefetching capabilities
- ❌ Poor multi-user support

### After (Granular Caching)
- ✅ Instant org switching with cached roles
- ✅ Granular cache updates per [email, org] combination
- ✅ Prefetching for smooth transitions
- ✅ Multi-user role caching support
- ✅ Real-time role updates without full refresh

## 🔮 Future Enhancements

### 1. Background Sync
- Periodic background refresh of cached roles
- Webhook integration for real-time role updates
- Conflict resolution for concurrent updates

### 2. Advanced Prefetching
- Predictive prefetching based on user behavior
- Bulk prefetching for frequently accessed organizations
- Smart cache warming strategies

### 3. Analytics and Monitoring
- Cache hit/miss ratio tracking
- Performance metrics for role fetching
- User behavior analytics for optimization

## 🛠️ Migration Guide

### For Existing Components
```typescript
// Old way
const { role, loading } = useOrgRole();

// New way (enhanced)
const { role, isLoading, switchToOrganization } = useEnhancedOrgRole();
```

### For Organization Switching
```typescript
// Old way
setCurrentOrgContext(orgId);

// New way (with prefetching)
await switchToOrganization(orgId);
```

### For Role Updates
```typescript
// Old way
queryClient.invalidateQueries(['permissions']);

// New way (granular)
updateRoleInCache(email, orgId, newRole, orgName);
```

## 📝 Implementation Notes

1. **Backward Compatibility**: Existing role checking systems continue to work
2. **Gradual Migration**: Components can be migrated to enhanced system incrementally
3. **Performance**: Enhanced system provides better performance for multi-org scenarios
4. **Debugging**: Enhanced logging and cache key visualization for easier debugging

## 🔗 Related Documentation

- [Organization Switching Implementation](./ORGANIZATION_SWITCHING_IMPLEMENTATION.md)
- [RBAC Implementation Guide](./RBAC_IMPLEMENTATION.md)
- [TanStack Query Integration](./TANSTACK_QUERY_INTEGRATION.md)
