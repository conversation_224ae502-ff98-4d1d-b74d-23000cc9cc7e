import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useAllDevices } from './use-api';
import { vi, expect, describe, it, afterEach } from 'vitest';

// Helper to wrap hook in QueryClientProvider
const createWrapper = () => {
  const queryClient = new QueryClient();
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('useAllDevices', () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('returns device data on success', async () => {
    // Mock fetch to return a successful response
    vi.spyOn(global, 'fetch').mockResolvedValueOnce({
      ok: true,
      json: async () => [{ name: 'Device 1' }, { name: 'Device 2' }],
    } as any);

    const { result } = renderHook(() => useAllDevices(), { wrapper: createWrapper() });

    await waitFor(() => expect(result.current.data).toBeDefined());
    expect(result.current.data).toEqual([{ name: 'Device 1' }, { name: 'Device 2' }]);
    expect(result.current.isSuccess).toBe(true);
  });

  it('handles fetch failure', async () => {
    // Mock fetch to return a failed response
    vi.spyOn(global, 'fetch').mockResolvedValueOnce({
      ok: false,
      status: 500,
    } as any);

    const { result } = renderHook(() => useAllDevices(), { wrapper: createWrapper() });

    await waitFor(() => expect(result.current.isError).toBe(true));
    expect(result.current.data).toBeUndefined();
  });
});
