import { useMemo } from 'react';

interface PasswordValidation {
  minLength: boolean;
  hasNumber: boolean;
  hasSpecial: boolean;
  match: boolean;
  isValid: boolean;
  isFormValid: boolean;
}

export function usePasswordValidation(
  password: string,
  confirmPassword: string,
): PasswordValidation {
  return useMemo(() => {
    const validation = {
      minLength: password.length >= 8,
      hasNumber: /\d/.test(password),
      hasSpecial: /[!@#$%^&*(),.?":{}|<>]/.test(password),
      match: password === confirmPassword && password.length > 0,
    };

    const isValid = validation.minLength && validation.hasNumber && validation.hasSpecial;
    const isFormValid = isValid && validation.match;

    return {
      ...validation,
      isValid,
      isFormValid,
    };
  }, [password, confirmPassword]);
}
