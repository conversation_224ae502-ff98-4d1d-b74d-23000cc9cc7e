'use client';

import React, { useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Permission } from '@/types/auth';
import { useOrgContext } from '@/components/org/org-context-provider';

interface PermissionResponse {
  success: boolean;
  permissions: Permission[];
  roles: string[];
  timestamp: string;
  source?: string; // Added for debugging
  cached?: boolean; // Added for debugging
  duration?: string; // Added for debugging
  error?: string;
  orgRoles?: OrgRoles; // Added for multi-org support
  orgContext?: {
    requestedOrgId?: string;
    isOrgSpecific?: boolean;
    scope?: string;
    currentOrg?: any;
  }; // Added for organization context
}

interface OrgRole {
  orgId: string;
  orgName: string;
  role: string;
  updated: string;
}

interface OrgRoles {
  [orgId: string]: OrgRole;
}

// Enhanced role structure for granular caching
interface UserOrgRole {
  emailId: string;
  orgId: string;
  role: string;
  orgName: string;
  updated: string;
}

interface EnhancedRoleCache {
  [key: string]: UserOrgRole; // key format: "email:orgId"
}

interface RefreshRolesResponse {
  success: boolean;
  message: string;
  data: {
    email: string;
    roles: string[];
    timestamp: string;
  };
}

// Query keys for TanStack Query with enhanced role caching
export const permissionKeys = {
  all: ['permissions'] as const,
  current: () => [...permissionKeys.all, 'current'] as const,
  refresh: () => [...permissionKeys.all, 'refresh'] as const,
  // Enhanced role caching keys for [emailId, orgId, role] structure
  userOrgRole: (email: string, orgId: string) => ['userOrgRole', email, orgId] as const,
  userAllOrgRoles: (email: string) => ['userOrgRoles', email] as const,
  orgRoles: (orgId: string) => ['orgRoles', orgId] as const,
};

/**
 * Fetch current user's permissions and roles from API
 */
async function fetchPermissions(): Promise<PermissionResponse> {
  const requestId = Math.random().toString(36).substring(2, 15);
  const startTime = Date.now();

  console.log(`🚀 [USE-PERMISSIONS] ========== STARTING PERMISSIONS FETCH ==========`);
  console.log(`🚀 [USE-PERMISSIONS] Request ID: ${requestId}`);
  console.log(`🚀 [USE-PERMISSIONS] Timestamp: ${new Date().toISOString()}`);
  console.log(`🚀 [USE-PERMISSIONS] Calling API endpoint: /api/auth/permissions`);

  try {
    const response = await fetch('/api/auth/permissions');
    const fetchTime = Date.now() - startTime;

    console.log(`📋 [USE-PERMISSIONS] API response received in ${fetchTime}ms:`, {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
      headers: {
        contentType: response.headers.get('content-type'),
        contentLength: response.headers.get('content-length'),
      },
      requestId,
    });

    if (!response.ok) {
      console.error(`❌ [USE-PERMISSIONS] API request failed:`, {
        status: response.status,
        statusText: response.statusText,
        requestId,
        fetchTime: `${fetchTime}ms`,
      });
      throw new Error(`Failed to fetch permissions: ${response.status}`);
    }

    const parseStart = Date.now();
    const data = await response.json();
    const parseTime = Date.now() - parseStart;
    const totalTime = Date.now() - startTime;

    console.log(`📊 [USE-PERMISSIONS] Response parsed in ${parseTime}ms`);
    console.log(`📊 [USE-PERMISSIONS] API response data:`, {
      hasRoles: !!(data.roles && data.roles.length > 0),
      rolesCount: data.roles?.length || 0,
      roles: data.roles || [],
      hasPermissions: !!(data.permissions && data.permissions.length > 0),
      permissionsCount: data.permissions?.length || 0,
      permissions: data.permissions || [],
      source: data.source || 'unknown',
      cached: data.cached || false,
      duration: data.duration || 'unknown',
      requestId,
    });

    console.log(
      `✅ [USE-PERMISSIONS] ========== PERMISSIONS FETCH COMPLETED in ${totalTime}ms ==========`,
    );
    console.log(`✅ [USE-PERMISSIONS] Final summary:`, {
      rolesCount: data.roles?.length || 0,
      permissionsCount: data.permissions?.length || 0,
      source: data.source || 'unknown',
      totalTime: `${totalTime}ms`,
      apiTime: `${fetchTime}ms`,
      parseTime: `${parseTime}ms`,
      requestId,
    });

    return data;
  } catch (error: any) {
    const totalTime = Date.now() - startTime;
    console.error(
      `❌ [USE-PERMISSIONS] ========== PERMISSIONS FETCH FAILED after ${totalTime}ms ==========`,
    );
    console.error(`❌ [USE-PERMISSIONS] Fetch error:`, {
      errorMessage: error.message,
      errorName: error.name,
      errorStack: error.stack,
      requestId,
      totalTime: `${totalTime}ms`,
    });
    throw error;
  }
}

/**
 * Organization-aware permissions fetch function
 * Supports both global and organization-specific permission fetching
 */
async function fetchOrgPermissions(orgId?: string): Promise<PermissionResponse> {
  const requestId = Math.random().toString(36).substring(2, 15);
  const startTime = Date.now();

  console.log(
    `🚀 [USE-ORG-PERMISSIONS] ========== STARTING ORG-AWARE PERMISSIONS FETCH ==========`,
  );
  console.log(`🚀 [USE-ORG-PERMISSIONS] Request ID: ${requestId}`);
  console.log(`🚀 [USE-ORG-PERMISSIONS] Timestamp: ${new Date().toISOString()}`);
  console.log(`🚀 [USE-ORG-PERMISSIONS] Organization context:`, {
    orgId: orgId || 'global',
    isOrgSpecific: !!orgId,
    scope: orgId ? 'organization-specific' : 'global',
  });

  try {
    // Build URL with optional orgId parameter
    const url = new URL('/api/auth/permissions', window.location.origin);
    if (orgId) {
      url.searchParams.set('orgId', orgId);
    }

    console.log(`📤 [USE-ORG-PERMISSIONS] Calling API endpoint: ${url.toString()}`);

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    const fetchTime = Date.now() - startTime;

    console.log(`📋 [USE-ORG-PERMISSIONS] API response received in ${fetchTime}ms:`, {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
      orgId: orgId || 'global',
      requestId,
    });

    if (!response.ok) {
      console.error(`❌ [USE-ORG-PERMISSIONS] API request failed:`, {
        status: response.status,
        statusText: response.statusText,
        orgId: orgId || 'global',
        requestId,
        fetchTime: `${fetchTime}ms`,
      });
      throw new Error(`Failed to fetch org permissions: ${response.status}`);
    }

    const parseStart = Date.now();
    const data = await response.json();
    const parseTime = Date.now() - parseStart;
    const totalTime = Date.now() - startTime;

    console.log(`📊 [USE-ORG-PERMISSIONS] Response parsed in ${parseTime}ms`);
    console.log(`📊 [USE-ORG-PERMISSIONS] API response data:`, {
      hasRoles: !!(data.roles && data.roles.length > 0),
      rolesCount: data.roles?.length || 0,
      roles: data.roles || [],
      hasPermissions: !!(data.permissions && data.permissions.length > 0),
      permissionsCount: data.permissions?.length || 0,
      source: data.source || 'unknown',
      cached: data.cached || false,
      orgContext: data.orgContext || null,
      duration: data.duration || 'unknown',
      requestId,
    });

    console.log(
      `✅ [USE-ORG-PERMISSIONS] ========== ORG-AWARE PERMISSIONS FETCH COMPLETED in ${totalTime}ms ==========`,
    );

    return data;
  } catch (error: any) {
    const totalTime = Date.now() - startTime;
    console.error(
      `❌ [USE-ORG-PERMISSIONS] ========== ORG-AWARE PERMISSIONS FETCH FAILED after ${totalTime}ms ==========`,
    );
    console.error(`❌ [USE-ORG-PERMISSIONS] Fetch error:`, {
      errorMessage: error.message,
      errorName: error.name,
      orgId: orgId || 'global',
      requestId,
      totalTime: `${totalTime}ms`,
    });
    throw error;
  }
}

/**
 * Refresh user roles from external QBraid API
 */
async function refreshRoles(): Promise<RefreshRolesResponse> {
  const response = await fetch('/api/refresh-roles', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
  });

  if (!response.ok) {
    throw new Error(`Failed to refresh roles: ${response.status}`);
  }

  return response.json();
}

/**
 * Enhanced permissions hook using TanStack Query
 * Provides automatic caching, background updates, and optimistic updates
 */
export function usePermissions() {
  console.log(`🔍 [USE-PERMISSIONS-HOOK] ========== HOOK CALLED ==========`);
  console.log(`🔍 [USE-PERMISSIONS-HOOK] Timestamp: ${new Date().toISOString()}`);

  const queryClient = useQueryClient();

  // Check if we have cached data before making the query
  const existingData = queryClient.getQueryData(permissionKeys.current()) as
    | PermissionResponse
    | undefined;
  console.log(`📋 [USE-PERMISSIONS-HOOK] Existing cached data:`, {
    hasCachedData: !!existingData,
    cachedRoles: existingData?.roles || [],
    cachedPermissions: existingData?.permissions || [],
    rolesCount: existingData?.roles?.length || 0,
    permissionsCount: existingData?.permissions?.length || 0,
  });

  // Main permissions query with optimized caching
  const { data, isLoading, error, isError, refetch, isStale, isFetching } = useQuery({
    queryKey: permissionKeys.current(),
    queryFn: () => {
      console.log(`🔥 [TANSTACK-QUERY] Query function called! About to call fetchPermissions()`);
      return fetchPermissions();
    },
    staleTime: 10 * 60 * 1000, // 10 minutes - longer stale time
    gcTime: 30 * 60 * 1000, // 30 minutes - keep in cache longer
    refetchOnWindowFocus: false, // Don't refetch when user returns to tab
    refetchOnMount: true, // Always fetch on mount to ensure fresh data
    refetchOnReconnect: true, // Only refetch on network reconnection
    retry: 2, // Fewer retries
    retryDelay: 1000, // Fixed delay instead of exponential
    enabled: true, // Explicitly enable the query
  });

  console.log(`📊 [USE-PERMISSIONS-HOOK] Query state:`, {
    isLoading: isLoading,
    isFetching: isFetching,
    isStale: isStale,
    isError: isError,
    hasData: !!data,
    hasError: !!error,
    errorMessage: error?.message || null,
    dataAge: data?.timestamp ? Date.now() - new Date(data.timestamp).getTime() : 'unknown',
  });

  // Mutation for refreshing roles from external API
  const refreshRolesMutation = useMutation({
    mutationFn: refreshRoles,
    onSuccess: (data) => {
      console.log(`✅ [USE-PERMISSIONS-HOOK] Roles refresh mutation succeeded:`, {
        email: data.data.email,
        rolesCount: data.data.roles.length,
        roles: data.data.roles,
        timestamp: data.data.timestamp,
      });

      // Invalidate and refetch permissions after successful role refresh
      queryClient.invalidateQueries({ queryKey: permissionKeys.current() });
      console.log(`🔄 [USE-PERMISSIONS-HOOK] Invalidated permissions cache after role refresh`);

      // Optionally show success notification
      console.log('✅ [PERMISSIONS] Roles refreshed:', data.data.roles);
    },
    onError: (error) => {
      console.error(`❌ [USE-PERMISSIONS-HOOK] Roles refresh mutation failed:`, {
        errorMessage: error.message,
        errorName: error.name,
      });
      console.error('❌ [PERMISSIONS] Failed to refresh roles:', error);
    },
  });

  // Extract permissions and roles from data
  const permissions = data?.permissions || [];
  const roles = data?.roles || [];
  const orgRoles = data?.orgRoles || {}; // Extract multi-org data

  console.log(`📋 [USE-PERMISSIONS-HOOK] Extracted data:`, {
    rolesCount: roles.length,
    roles: roles,
    permissionsCount: permissions.length,
    permissions: permissions,
    orgRolesCount: Object.keys(orgRoles).length,
    orgRoles: orgRoles,
    source: data?.source || 'unknown',
    cached: data?.cached || false,
  });

  // Multi-org utility functions
  const getOrganizations = (): OrgRole[] => {
    const orgs = Object.values(orgRoles);
    console.log(`🏢 [ORG-UTILS] getOrganizations():`, {
      orgCount: orgs.length,
      organizations: orgs,
    });
    return orgs;
  };

  const getUserRoleInOrg = (orgId: string): string | null => {
    const role = orgRoles[orgId]?.role || null;
    console.log(`🏢 [ORG-UTILS] getUserRoleInOrg("${orgId}"):`, {
      orgId,
      role,
      orgExists: !!orgRoles[orgId],
    });
    return role;
  };

  const getOrganizationById = (orgId: string): OrgRole | null => {
    const org = orgRoles[orgId] || null;
    console.log(`🏢 [ORG-UTILS] getOrganizationById("${orgId}"):`, {
      orgId,
      found: !!org,
      org,
    });
    return org;
  };

  const getOrganizationsByRole = (targetRole: string): OrgRole[] => {
    const matchingOrgs = Object.values(orgRoles).filter((org) => org.role === targetRole);
    console.log(`🏢 [ORG-UTILS] getOrganizationsByRole("${targetRole}"):`, {
      targetRole,
      matchCount: matchingOrgs.length,
      organizations: matchingOrgs,
    });
    return matchingOrgs;
  };

  const isAdminInAnyOrg = (): boolean => {
    const result = Object.values(orgRoles).some((org) => org.role === 'admin');
    console.log(`🏢 [ORG-UTILS] isAdminInAnyOrg():`, {
      result,
      totalOrgs: Object.keys(orgRoles).length,
      adminOrgs: getOrganizationsByRole('admin').length,
    });
    return result;
  };

  const isAdminInOrg = (orgId: string): boolean => {
    const result = orgRoles[orgId]?.role === 'admin';
    console.log(`🏢 [ORG-UTILS] isAdminInOrg("${orgId}"):`, {
      orgId,
      result,
      userRole: orgRoles[orgId]?.role || 'none',
    });
    return result;
  };

  const getCurrentOrgContext = (): string | null => {
    // Check localStorage for persisted organization selection
    if (typeof window !== 'undefined') {
      const savedOrgId = localStorage.getItem('currentOrgId');
      if (savedOrgId && orgRoles[savedOrgId]) {
        console.log(`🏢 [ORG-UTILS] getCurrentOrgContext(): Using saved org from localStorage`, {
          savedOrgId,
          orgExists: !!orgRoles[savedOrgId],
        });
        return savedOrgId;
      }
    }

    // Fallback to first org if only one exists
    const orgIds = Object.keys(orgRoles);
    const currentOrg = orgIds.length === 1 ? orgIds[0] : orgIds[0] || null;
    console.log(`🏢 [ORG-UTILS] getCurrentOrgContext(): Using fallback org`, {
      totalOrgs: orgIds.length,
      currentOrg,
      suggestion: orgIds.length > 1 ? 'User should select an org' : 'Single org detected',
    });
    return currentOrg;
  };

  const setCurrentOrgContext = (orgId: string): void => {
    if (typeof window !== 'undefined' && orgRoles[orgId]) {
      localStorage.setItem('currentOrgId', orgId);
      console.log(`🏢 [ORG-UTILS] setCurrentOrgContext(): Org context updated`, {
        newOrgId: orgId,
        orgName: orgRoles[orgId]?.orgName,
        role: orgRoles[orgId]?.role,
      });

      // Trigger a custom event to notify other components
      window.dispatchEvent(
        new CustomEvent('org-context-changed', {
          detail: { orgId, org: orgRoles[orgId] },
        }),
      );
    }
  };

  // Permission checking utilities
  const hasPermission = (permission: Permission): boolean => {
    const result = permissions.includes(permission) || permissions.includes(Permission.AdminAccess);
    console.log(`🔍 [PERMISSION-CHECK] hasPermission("${permission}"):`, {
      permission,
      result,
      hasAdminAccess: permissions.includes(Permission.AdminAccess),
      currentPermissions: permissions,
    });
    return result;
  };

  const hasAnyPermission = (requiredPermissions: Permission[]): boolean => {
    const result = requiredPermissions.some((permission) => hasPermission(permission));
    console.log(`🔍 [PERMISSION-CHECK] hasAnyPermission([${requiredPermissions.join(', ')}]):`, {
      requiredPermissions,
      result,
      currentPermissions: permissions,
    });
    return result;
  };

  const hasAllPermissions = (requiredPermissions: Permission[]): boolean => {
    const result = requiredPermissions.every((permission) => hasPermission(permission));
    console.log(`🔍 [PERMISSION-CHECK] hasAllPermissions([${requiredPermissions.join(', ')}]):`, {
      requiredPermissions,
      result,
      currentPermissions: permissions,
    });
    return result;
  };

  const hasRole = (role: string): boolean => {
    const result = roles.includes(role);
    console.log(`🔍 [ROLE-CHECK] hasRole("${role}"):`, {
      role,
      result,
      currentRoles: roles,
    });
    return result;
  };

  const hasAnyRole = (requiredRoles: string[]): boolean => {
    const result = requiredRoles.some((role) => hasRole(role));
    console.log(`🔍 [ROLE-CHECK] hasAnyRole([${requiredRoles.join(', ')}]):`, {
      requiredRoles,
      result,
      currentRoles: roles,
    });
    return result;
  };

  const isAdmin = (): boolean => {
    const result = hasPermission(Permission.AdminAccess);
    console.log(`🔍 [ADMIN-CHECK] isAdmin():`, {
      result,
      hasAdminPermission: permissions.includes(Permission.AdminAccess),
      currentPermissions: permissions,
    });
    return result;
  };

  // Refresh functions
  const refreshPermissions = () => {
    console.log(`🔄 [USE-PERMISSIONS-HOOK] refreshPermissions() called - triggering refetch`);
    refetch();
  };

  const refreshRolesFromAPI = () => {
    console.log(`🔄 [USE-PERMISSIONS-HOOK] refreshRolesFromAPI() called - triggering mutation`);
    refreshRolesMutation.mutate();
  };

  console.log(`📊 [USE-PERMISSIONS-HOOK] Hook returning:`, {
    rolesCount: roles.length,
    permissionsCount: permissions.length,
    orgRolesCount: Object.keys(orgRoles).length,
    isLoading: isLoading,
    isRefreshing: refreshRolesMutation.isPending,
    hasError: isError,
    isStale: data ? Date.now() - new Date(data.timestamp).getTime() > 5 * 60 * 1000 : false,
  });

  console.log(`✅ [USE-PERMISSIONS-HOOK] ========== HOOK COMPLETED ==========`);

  return {
    // Data
    permissions,
    roles,
    orgRoles, // Expose orgRoles

    // Loading states
    loading: isLoading,
    isLoading,
    isRefreshing: refreshRolesMutation.isPending,

    // Error states
    error: error?.message || null,
    isError,
    refreshError: refreshRolesMutation.error?.message || null,

    // Permission checking utilities
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
    isAdmin,

    // Multi-org utility functions
    getOrganizations,
    getUserRoleInOrg,
    getOrganizationById,
    getOrganizationsByRole,
    isAdminInAnyOrg,
    isAdminInOrg,
    getCurrentOrgContext,
    setCurrentOrgContext,

    // Actions
    refreshPermissions,
    refreshRolesFromAPI,

    // Raw query data for advanced usage
    queryData: data,
    isStale: data ? Date.now() - new Date(data.timestamp).getTime() > 5 * 60 * 1000 : false,
  };
}

// Export types for use in other components
export type { OrgRole, OrgRoles, UserOrgRole, EnhancedRoleCache };

/**
 * Higher-order component for permission-based rendering with TanStack Query integration
 */
interface PermissionGuardProps {
  children: React.ReactNode;
  permission?: Permission;
  permissions?: Permission[];
  role?: string;
  roles?: string[];
  fallback?: React.ReactNode;
  requireAll?: boolean;
  showLoadingState?: boolean;
}

export function PermissionGuard({
  children,
  permission,
  permissions,
  role,
  roles,
  fallback = null,
  requireAll = false,
  showLoadingState = true,
}: PermissionGuardProps) {
  const { hasPermission, hasAnyPermission, hasAllPermissions, hasRole, hasAnyRole, loading } =
    usePermissions();

  // Show loading state if enabled and data is loading
  if (loading && showLoadingState) {
    return <div className="animate-pulse bg-gray-200 h-4 w-full rounded" />;
  }

  let hasAccess = true;

  // Check permissions
  if (permission) {
    hasAccess = hasPermission(permission);
  } else if (permissions) {
    hasAccess = requireAll ? hasAllPermissions(permissions) : hasAnyPermission(permissions);
  }

  // Check roles
  if (hasAccess && role) {
    hasAccess = hasRole(role);
  } else if (hasAccess && roles) {
    hasAccess = requireAll ? roles.every((r) => hasRole(r)) : hasAnyRole(roles);
  }

  return hasAccess ? <>{children}</> : <>{fallback}</>;
}

/**
 * Hook for invalidating permissions cache
 * Useful for components that modify user roles/permissions
 */
export function useInvalidatePermissions() {
  const queryClient = useQueryClient();

  return {
    invalidatePermissions: () => {
      queryClient.invalidateQueries({ queryKey: permissionKeys.current() });
    },

    setPermissionsData: (data: PermissionResponse) => {
      queryClient.setQueryData(permissionKeys.current(), data);
    },

    getPermissionsData: () => {
      return queryClient.getQueryData<PermissionResponse>(permissionKeys.current());
    },
  };
}

/**
 * Prefetch permissions for performance optimization
 */
export function usePrefetchPermissions() {
  const queryClient = useQueryClient();

  return () => {
    queryClient.prefetchQuery({
      queryKey: permissionKeys.current(),
      queryFn: fetchPermissions,
      staleTime: 5 * 60 * 1000,
    });
  };
}

/**
 * Enhanced Role Caching System
 * Stores roles in granular [emailId, orgId, role] format for dynamic organization switching
 */

/**
 * Fetch user role for a specific organization with enhanced caching
 */
async function fetchUserOrgRole(email: string, orgId: string): Promise<UserOrgRole | null> {
  try {
    const response = await fetch(`/api/user/roles?orgId=${orgId}&email=${email}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch role: ${response.status}`);
    }

    const data = await response.json();

    if (data.success && data.data) {
      return {
        emailId: email,
        orgId: orgId,
        role: data.data.currentUserRole || data.data.role,
        orgName: data.data.orgName || 'Unknown Organization',
        updated: new Date().toISOString(),
      };
    }

    return null;
  } catch (error) {
    console.error(
      `❌ [ENHANCED-ROLE-CACHE] Failed to fetch role for ${email} in org ${orgId}:`,
      error,
    );
    return null;
  }
}

/**
 * Hook for getting user role in a specific organization with enhanced caching
 */
export function useUserOrgRole(email: string, orgId: string, enabled: boolean = true) {
  return useQuery({
    queryKey: permissionKeys.userOrgRole(email, orgId),
    queryFn: () => fetchUserOrgRole(email, orgId),
    enabled: enabled && !!email && !!orgId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
  });
}

/**
 * Hook for getting all organization roles for a user
 */
export function useUserAllOrgRoles(email: string, enabled: boolean = true) {
  const { getOrganizations } = usePermissions();
  const organizations = getOrganizations();

  return useQuery({
    queryKey: permissionKeys.userAllOrgRoles(email),
    queryFn: async (): Promise<EnhancedRoleCache> => {
      const roleCache: EnhancedRoleCache = {};

      // Fetch roles for all organizations in parallel
      const rolePromises = organizations.map(async (org) => {
        const userRole = await fetchUserOrgRole(email, org.orgId);
        if (userRole) {
          const cacheKey = `${email}:${org.orgId}`;
          roleCache[cacheKey] = userRole;
        }
      });

      await Promise.all(rolePromises);
      return roleCache;
    },
    enabled: enabled && !!email && organizations.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
  });
}

/**
 * Organization-aware permissions hook
 * Automatically uses current organization context or allows explicit orgId
 *
 * @param orgId - Optional organization ID. If not provided, uses current org from context
 * @param options - Additional options for the hook
 */
export function useOrgPermissions(
  orgId?: string,
  options: {
    enabled?: boolean;
    fallbackToGlobal?: boolean;
  } = {},
) {
  const { enabled = true, fallbackToGlobal = true } = options;

  // Get current organization from context if no orgId provided
  const { currentOrgId } = useOrgContext();
  const effectiveOrgId = orgId || currentOrgId;

  console.log(`🔍 [USE-ORG-PERMISSIONS] Hook called with:`, {
    providedOrgId: orgId || 'none',
    currentOrgId: currentOrgId || 'none',
    effectiveOrgId: effectiveOrgId || 'none',
    enabled,
    fallbackToGlobal,
    timestamp: new Date().toISOString(),
  });

  // Create dynamic query key based on organization context
  const queryKey = effectiveOrgId
    ? ['permissions', 'org', effectiveOrgId]
    : ['permissions', 'global'];

  // Main permissions query with organization awareness
  const { data, isLoading, error, isError, refetch, isStale, isFetching } = useQuery({
    queryKey,
    queryFn: () => {
      console.log(`🔥 [USE-ORG-PERMISSIONS] TanStack Query function called!`, {
        effectiveOrgId: effectiveOrgId || 'global',
        queryKey,
      });
      return fetchOrgPermissions(effectiveOrgId || undefined);
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: true,
    retry: 2,
    retryDelay: 1000,
    enabled: enabled,
  });

  // Extract and process data
  const roles = data?.roles || [];
  const permissions = data?.permissions || [];
  const orgRoles = data?.orgRoles || {};
  const orgContext = data?.orgContext || null;

  // Enhanced permission checking functions
  const hasPermission = useCallback(
    (permission: Permission): boolean => {
      return permissions.includes(permission);
    },
    [permissions],
  );

  const hasRole = useCallback(
    (role: string): boolean => {
      return roles.includes(role);
    },
    [roles],
  );

  const hasAnyRole = useCallback(
    (rolesToCheck: string[]): boolean => {
      return rolesToCheck.some((role) => roles.includes(role));
    },
    [roles],
  );

  const hasAllRoles = useCallback(
    (rolesToCheck: string[]): boolean => {
      return rolesToCheck.every((role) => roles.includes(role));
    },
    [roles],
  );

  const getCurrentOrgRole = useCallback((): string | null => {
    if (!effectiveOrgId || !orgRoles[effectiveOrgId]) {
      return null;
    }
    return orgRoles[effectiveOrgId].role;
  }, [effectiveOrgId, orgRoles]);

  const refreshPermissions = useCallback(async () => {
    console.log(
      `🔄 [USE-ORG-PERMISSIONS] Refreshing permissions for org: ${effectiveOrgId || 'global'}`,
    );
    await refetch();
  }, [refetch, effectiveOrgId]);

  console.log(`📊 [USE-ORG-PERMISSIONS] Hook returning:`, {
    rolesCount: roles.length,
    permissionsCount: permissions.length,
    orgRolesCount: Object.keys(orgRoles).length,
    currentOrgRole: getCurrentOrgRole(),
    isLoading,
    isRefreshing: isFetching && !isLoading,
    hasError: isError,
    isOrgSpecific: !!effectiveOrgId,
    orgContext,
  });

  return {
    // Data
    roles,
    permissions,
    orgRoles,
    orgContext,

    // Organization context
    currentOrgId: effectiveOrgId,
    currentOrgRole: getCurrentOrgRole(),
    isOrgSpecific: !!effectiveOrgId,

    // Permission checking functions
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAllRoles,

    // State
    isLoading,
    isRefreshing: isFetching && !isLoading,
    hasError: isError,
    error: error as Error | null,
    isStale,

    // Actions
    refreshPermissions,
    refetch,

    // Metadata
    source: data?.source || 'unknown',
    cached: data?.cached || false,
    duration: data?.duration || 'unknown',
  };
}

/**
 * Enhanced hook for organization-aware role management
 * Provides dynamic role checking based on current organization context
 */
export function useEnhancedOrgRole(email?: string, orgId?: string) {
  const { getCurrentOrgContext, getOrganizations } = usePermissions();
  const queryClient = useQueryClient();

  // Use provided orgId or current context
  const targetOrgId = orgId || getCurrentOrgContext();
  const organizations = getOrganizations();
  const currentOrg = organizations.find((org) => org.orgId === targetOrgId);

  // Get user email from session if not provided
  const { data: sessionData } = useQuery({
    queryKey: ['session'],
    queryFn: async () => {
      const response = await fetch('/api/auth/session');
      if (!response.ok) return null;
      return response.json();
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  const userEmail = email || sessionData?.email || '';

  // Query for specific org role
  const {
    data: userOrgRole,
    isLoading,
    error,
  } = useUserOrgRole(userEmail, targetOrgId || '', !!userEmail && !!targetOrgId);

  // Function to switch organization and update cache
  const switchToOrganization = async (newOrgId: string) => {
    if (!userEmail) return;

    // Prefetch role for new organization
    await queryClient.prefetchQuery({
      queryKey: permissionKeys.userOrgRole(userEmail, newOrgId),
      queryFn: () => fetchUserOrgRole(userEmail, newOrgId),
      staleTime: 5 * 60 * 1000,
    });

    console.log(`🔄 [ENHANCED-ROLE-CACHE] Prefetched role for ${userEmail} in org ${newOrgId}`);
  };

  // Function to invalidate role cache for specific org
  const invalidateOrgRole = (targetEmail: string, targetOrgId: string) => {
    queryClient.invalidateQueries({
      queryKey: permissionKeys.userOrgRole(targetEmail, targetOrgId),
    });
  };

  // Function to update role in cache (for real-time updates)
  const updateRoleInCache = (
    targetEmail: string,
    targetOrgId: string,
    newRole: string,
    orgName?: string,
  ) => {
    const cacheKey = permissionKeys.userOrgRole(targetEmail, targetOrgId);
    const updatedRole: UserOrgRole = {
      emailId: targetEmail,
      orgId: targetOrgId,
      role: newRole,
      orgName: orgName || 'Unknown Organization',
      updated: new Date().toISOString(),
    };

    queryClient.setQueryData(cacheKey, updatedRole);
    console.log(
      `🔄 [ENHANCED-ROLE-CACHE] Updated role in cache: ${targetEmail} -> ${newRole} in ${targetOrgId}`,
    );
  };

  return {
    // Current role data
    userOrgRole,
    role: userOrgRole?.role || null,
    organization: currentOrg,
    isLoading,
    error,

    // Organization management
    switchToOrganization,
    invalidateOrgRole,
    updateRoleInCache,

    // Convenience methods
    isAdmin: () => userOrgRole?.role === 'admin' || userOrgRole?.role === 'owner',
    canManage: () => ['admin', 'owner'].includes(userOrgRole?.role || ''),
    canView: () => ['admin', 'owner', 'member', 'viewer'].includes(userOrgRole?.role || ''),
    hasRole: (role: string) => userOrgRole?.role === role,
  };
}
