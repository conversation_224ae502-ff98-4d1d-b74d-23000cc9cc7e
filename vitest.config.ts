// vitest.config.ts
//
// This file configures Vitest, our testing framework for React components.
// It tells <PERSON><PERSON><PERSON> how to find our tests, what environment to use, how to handle imports,
// and how to generate code coverage reports. Each section is explained below in plain English.

import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react'; // Lets us test React components
import path from 'path'; // Used to help resolve import paths

export default defineConfig({
  // Plugins let us use React features in our tests
  plugins: [react()],

  // This section helps Vitest understand our import paths.
  // The '@' symbol is set to mean the root of our project, so imports like '@/components/...' work in tests.
  resolve: {
    alias: {
      '@': path.resolve(__dirname, '.'),
    },
  },

  test: {
    // Use 'jsdom' so our tests can pretend to run in a web browser (needed for React components)
    environment: 'jsdom',

    // Only look for test files in the components or api-calls folder that end with .test.tsx or .test.ts
    include: ['components/**/*.test.{ts,tsx}', 'api-calls/**/*.test.{ts,tsx}'],

    // This section sets up code coverage, so we know how much of our code is tested
    coverage: {
      reporter: ['text', 'json', 'html'], // 'text' ensures coverage is shown in the terminal
      provider: 'v8', // Use the built-in coverage tool
      all: true, // Check coverage for all files, not just the ones with tests
      include: [
        'components/**/*.tsx', // Only measure coverage for our component files
        'api-calls/**/*.tsx', // Also measure coverage for API utility files
      ],
      exclude: ['**/*.test.{ts,tsx}'], // Don't count test files themselves
    },

    // Allow using global test functions like 'describe', 'it', and 'expect' without importing them everywhere
    globals: true,

    // Run this setup file before tests to add extra matchers (like toBeInTheDocument)
    setupFiles: ['./vitest.setup.ts'],
  },
});
