'use client';

import { useEffect, useState } from 'react';

/**
 * Hook to fetch and manage CSRF tokens for form protection
 */
export function useCSRFToken() {
  const [csrfToken, setCSRFToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let mounted = true;

    async function fetchCSRFToken() {
      try {
        const response = await fetch('/api/csrf-token', {
          method: 'GET',
          credentials: 'same-origin',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch CSRF token');
        }

        const data = await response.json();

        if (mounted) {
          setCSRFToken(data.csrfToken);
          setError(null);
        }
      } catch (err) {
        if (mounted) {
          setError(err instanceof Error ? err.message : 'Unknown error');
          setCSRFToken(null);
        }
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    }

    fetchCSRFToken();

    return () => {
      mounted = false;
    };
  }, []);

  return { csrfToken, loading, error };
}

/**
 * Component for CSRF token hidden input field
 */
interface CSRFTokenInputProps {
  csrfToken?: string | null;
}

export function CSRFTokenInput({ csrfToken }: CSRFTokenInputProps) {
  if (!csrfToken) {
    return null;
  }

  return <input type="hidden" name="csrfToken" value={csrfToken} required />;
}

/**
 * Higher-order component to automatically include CSRF protection
 */
interface WithCSRFProtectionProps {
  children: (csrfToken: string | null, loading: boolean, error: string | null) => React.ReactNode;
}

export function WithCSRFProtection({ children }: WithCSRFProtectionProps) {
  const { csrfToken, loading, error } = useCSRFToken();

  return <>{children(csrfToken, loading, error)}</>;
}

/**
 * Utility function to add CSRF token to form data
 */
export function addCSRFToFormData(formData: FormData, csrfToken: string): FormData {
  if (csrfToken) {
    formData.set('csrfToken', csrfToken);
  }
  return formData;
}

/**
 * Utility function to create fetch options with CSRF protection
 */
export function createCSRFProtectedFetchOptions(
  csrfToken: string | null,
  options: RequestInit = {},
): RequestInit {
  const headers = new Headers(options.headers);

  if (csrfToken) {
    headers.set('X-CSRF-Token', csrfToken);
  }

  return {
    ...options,
    headers,
    credentials: 'same-origin',
  };
}
