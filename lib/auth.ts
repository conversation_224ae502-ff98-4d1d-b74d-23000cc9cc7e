import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

export async function getAuthSession() {
  const cookieStore = await cookies();
  const token = cookieStore.get('amplify-auth-token');

  if (!token) {
    return null;
  }

  try {
    const session = JSON.parse(token.value);

    // Check if session is expired (older than 7 days)
    if (Date.now() - session.timestamp > 7 * 24 * 60 * 60 * 1000) {
      cookieStore.delete('amplify-auth-token');
      return null;
    }

    return session;
  } catch (error) {
    cookieStore.delete('amplify-auth-token');
    return null;
  }
}

export async function requireAuth() {
  const session = await getAuthSession();
  if (!session) {
    redirect('/signin');
  }
  return session;
}

export async function requireNoAuth() {
  const session = await getAuthSession();
  if (session) {
    redirect('/');
  }
}
