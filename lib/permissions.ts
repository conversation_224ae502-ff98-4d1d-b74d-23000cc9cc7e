import { Permission, ExternalRoleMapping } from '@/types/auth';

/**
 * Mapping from external API roles to internal permissions
 * Customize this based on your third-party API's role names
 */
export const externalRoleToPermissions: ExternalRoleMapping = {
  admin: [
    Permission.ViewDevices,
    // Permission.ManageDevices,
    // Permission.ViewProfile,
    // Permission.EditProfile,
    // Permission.ViewTeam,
    // Permission.ManageTeam,
    // Permission.ViewEarnings,
    // Permission.ManageEarnings,
    // Permission.ViewJobs,
    // Permission.ManageJobs,
    Permission.AdminAccess,
  ],
  member: [
    Permission.ViewDevices,
    Permission.ViewProfile,
    Permission.EditProfile,
    Permission.ViewEarnings,
    Permission.ViewJobs,
  ],
  viewer: [
    Permission.ViewDevices,
    Permission.ViewProfile,
    Permission.ViewEarnings,
    Permission.ViewJobs,
  ],
  owner: [
    Permission.ViewDevices,
    Permission.ManageDevices,
    Permission.ViewProfile,
    Permission.EditProfile,
    Permission.ViewTeam,
    Permission.ManageTeam,
    Permission.ViewEarnings,
    Permission.ManageEarnings,
    Permission.ViewJobs,
    Permission.ManageJobs,
    Permission.AdminAccess,
  ],
};

/**
 * Route-to-permission mapping for middleware protection
 */
export const routePermissions: Record<string, Permission> = {
  '/devices': Permission.ViewDevices,
  '/edit-device': Permission.ManageDevices,
  '/profile': Permission.ViewProfile,
  '/team': Permission.ViewTeam,
  '/earnings': Permission.ViewEarnings,
  '/rbac-demo': Permission.ViewDevices, // Demo accessible to anyone with basic permissions
};

/**
 * Map external roles to internal permissions
 */
export function mapRolesToPermissions(externalRoles: string[]): Permission[] {
  const permissions = new Set<Permission>();

  externalRoles.forEach((role) => {
    const rolePermissions = externalRoleToPermissions[role];
    if (rolePermissions) {
      rolePermissions.forEach((permission) => permissions.add(permission));
    }
  });

  return Array.from(permissions);
}

/**
 * Check if user has required permission
 */
export function hasPermission(
  userPermissions: Permission[],
  requiredPermission: Permission,
): boolean {
  return (
    userPermissions.includes(requiredPermission) || userPermissions.includes(Permission.AdminAccess)
  );
}

/**
 * Check if user has any of the required permissions
 */
export function hasAnyPermission(
  userPermissions: Permission[],
  requiredPermissions: Permission[],
): boolean {
  return requiredPermissions.some((permission) => hasPermission(userPermissions, permission));
}

/**
 * Get permissions for a specific route
 */
export function getRoutePermission(pathname: string): Permission | null {
  // Check exact match first
  if (routePermissions[pathname]) {
    return routePermissions[pathname];
  }

  // Check for path prefix matches
  for (const [route, permission] of Object.entries(routePermissions)) {
    if (pathname.startsWith(route)) {
      return permission;
    }
  }

  return null;
}
