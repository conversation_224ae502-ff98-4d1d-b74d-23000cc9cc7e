// Provides QueryClient instances for use with @tanstack/react-query throughout the app.
import { QueryClient } from '@tanstack/react-query';

// Default options for all queries
const defaultOptions = {
  queries: {
    staleTime: 60 * 1000, // 1 minute
    refetchOnWindowFocus: false,
  },
};

// Function to create a new QueryClient instance
function makeQueryClient() {
  return new QueryClient({
    defaultOptions,
  });
}

let browserQueryClient: QueryClient | undefined = undefined;

// Get QueryClient instance
export function getQueryClient() {
  if (typeof window === 'undefined') {
    // Server: always make a new query client
    return makeQueryClient();
  } else {
    // Browser: make a new query client if we don't already have one
    // This is very important, so we don't re-make a new client if React
    // suspends during the initial render. This may not be needed if we
    // have a suspense boundary BELOW the creation of the query client
    if (!browserQueryClient) browserQueryClient = makeQueryClient();
    return browserQueryClient;
  }
}

// Legacy export for backward compatibility
export const queryClient = getQueryClient();
