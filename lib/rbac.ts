'use server';

import { redirect } from 'next/navigation';
import { Permission } from '@/types/auth';
import { getSession } from './session';
import { hasPermission, hasAnyPermission } from './permissions';

/**
 * Require specific permission for server actions
 * Throws error or redirects if permission is missing
 */
export async function requirePermission(
  permission: Permission,
  redirectTo?: string,
): Promise<void> {
  const session = await getSession();

  if (!session) {
    if (redirectTo) {
      redirect('/signin');
    }
    throw new Error('Authentication required');
  }

  if (!hasPermission(session.permissions, permission)) {
    if (redirectTo) {
      redirect('/unauthorized');
    }
    throw new Error(`Permission denied: ${permission}`);
  }
}

/**
 * Require any of the specified permissions
 */
export async function requireAnyPermission(
  permissions: Permission[],
  redirectTo?: string,
): Promise<void> {
  const session = await getSession();

  if (!session) {
    if (redirectTo) {
      redirect('/signin');
    }
    throw new Error('Authentication required');
  }

  if (!hasAnyPermission(session.permissions, permissions)) {
    if (redirectTo) {
      redirect('/unauthorized');
    }
    throw new Error(`Permission denied: requires one of ${permissions.join(', ')}`);
  }
}

/**
 * Check if current user has permission (non-throwing)
 */
export async function checkPermission(permission: Permission): Promise<boolean> {
  const session = await getSession();

  if (!session) {
    return false;
  }

  return hasPermission(session.permissions, permission);
}

/**
 * Get current user's permissions
 */
export async function getCurrentPermissions(): Promise<Permission[]> {
  const session = await getSession();
  return session?.permissions || [];
}

/**
 * Get current user's external roles
 */
export async function getCurrentRoles(): Promise<string[]> {
  const session = await getSession();
  return session?.externalRoles || [];
}

/**
 * Check if user is admin
 */
export async function isAdmin(): Promise<boolean> {
  return await checkPermission(Permission.AdminAccess);
}

/**
 * Require admin access
 */
export async function requireAdmin(redirectTo?: string): Promise<void> {
  await requirePermission(Permission.AdminAccess, redirectTo);
}
