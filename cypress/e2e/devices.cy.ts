// cypress/e2e/devices.cy.ts
// This is an end-to-end (E2E) test for the /devices page using Cypress.
// It checks that the page loads and displays expected content.

describe('Devices Page', () => {
  // Before each test, visit the /devices page (update baseUrl as needed)
  beforeEach(() => {
    cy.visit('http://localhost:3000/devices');
  });

  it('should load and show the Devices heading', () => {
    // Check for a heading or text that should always be present on the Devices page
    cy.contains('Devices').should('exist');
  });

  it('should display at least one device card', () => {
    // Check for at least one device card by looking for a unique element in the card
    // Here, we look for the device name heading (h2) as rendered in DeviceCard
    cy.get('h2').should('have.length.greaterThan', 0);
  });

  it('should show device details when a card is present', () => {
    // Check for the "Device Details" section in the first card
    cy.contains('Device Details').should('exist');
  });

  it('should allow switching tabs on a device card', () => {
    cy.contains('Jobs').click();
    cy.contains('Topology').click();
    cy.contains('Calibration').click();
    // You can add more assertions here if the tab content is predictable
  });
});
