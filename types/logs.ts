// Action log-related types for use across the app

/**
 * Represents a single action log row.
 */
export interface ActionLogRowProps {
  timestamp: any;
  user: any;
  createdAt?: Date | string;
  action: string;
  userEmail: string;
  actionDescription?: string;
  userRole?: string;
}

/**
 * Represents a collection of action logs for display.
 */
export interface LogDisplayProps {
  logs: ActionLogRowProps[];
}
