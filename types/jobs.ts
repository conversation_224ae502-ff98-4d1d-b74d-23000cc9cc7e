// Job-related types for use across the app

/**
 * Represents a single job row.
 */
export interface JobsRowProps {
  qbraidDeviceId: string;
  qbraidStatus?: string;
  vendor?: string;
  provider?: string;
  experimentType?: string;
  shots?: number;
  cost?: number;
  timeStamps?: {
    createdAt?: Date | string;
    endedAt?: Date | string;
  };
  queuePosition?: number;
}

/**
 * Represents a collection of jobs for display.
 */
export interface JobsDisplayProps {
  jobsArray: JobsRowProps[];
}
