// Team and organization-related types for use across the app

/**
 * Represents a team member/user.
 */
export interface TeamMember {
  name: string;
  email: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  role: string;
  status: 'Active' | 'Invited' | 'Deactivated';
  userCredits?: number;
  lastActive?: string;
}

/**
 * Represents organization information.
 */
export interface OrgInfo {
  orgName: string;
  orgID: string;
  orgDescription?: string;
  orgEmail?: string;
  orgOwnerEmail?: string;
  orgLogo?: string;
  orgLastUpdate?: string;
  /** The signed-in user's role inside this organisation (owner, admin, etc.) */
  role?: string;
}

// ---------------------------------------------------------------------------
// 💡 Organisation list response (GET /api/orgs/get/:page/:limit)
// Only the fields currently needed by the front-end are typed – extend as
// required once the UI needs more data.
// ---------------------------------------------------------------------------

export interface OrgWithRole {
  org: {
    organization: Record<string, any>; // raw organisation document
    role: string; // caller's role inside this organisation
  };
  orgBilling: Record<string, any>;
}

export interface OrgListResponse {
  organizations: OrgWithRole[];
  pagination: {
    currentPage: number;
    limit: string;
    totalPages: number;
    totalOrganizations: number;
  };
}
