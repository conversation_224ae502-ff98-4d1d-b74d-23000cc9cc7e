# Figma design to code

_Automatically synced with your [v0.dev](https://v0.dev) deployments_

[![Deployed on Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-black?style=for-the-badge&logo=vercel)](https://vercel.com/pugglybluu-9582s-projects/v0-figma-design-to-code)
[![Built with v0](https://img.shields.io/badge/Built%20with-v0.dev-black?style=for-the-badge)](https://v0.dev/chat/projects/KUGVEeAZoxm)

## Overview

This repository will stay in sync with your deployed chats on [v0.dev](https://v0.dev).
Any changes you make to your deployed app will be automatically pushed to this repository from [v0.dev](https://v0.dev).

## Deployment

Your project is live at:

**[https://vercel.com/pugglybluu-9582s-projects/v0-figma-design-to-code](https://vercel.com/pugglybluu-9582s-projects/v0-figma-design-to-code)**

## Build your app

Continue building your app on:

**[https://v0.dev/chat/projects/KUGVEeAZoxm](https://v0.dev/chat/projects/KUGVEeAZoxm)**

## How It Works

1. Create and modify your project using [v0.dev](https://v0.dev)
2. Deploy your chats from the v0 interface
3. Changes are automatically pushed to this repository
4. Vercel deploys the latest version from this repository

# QBraid Partner Dashboard

A Next.js application for managing quantum devices and partner organizations with enterprise-grade authentication and role-based access control.

## Features

- 🔐 **AWS Cognito Authentication** with secure session management
- 🛡️ **Role-Based Access Control (RBAC)** integrated with QBraid organizations
- ⚡ **TanStack Query** for optimized client-side state management
- 🔄 **Redis-backed Sessions** for scalable session storage
- 📊 **Real-time Permission Updates** with multi-tab synchronization
- 🎯 **Type-safe Permissions** with comprehensive TypeScript support

## Quick Start

1. **Install dependencies:**

   ```bash
   npm install
   ```

2. **Configure environment variables:**

   ```bash
   cp .env.example .env.local
   # Update with your AWS Cognito, Redis, and QBraid API credentials
   ```

3. **Start development server:**
   ```bash
   npm run dev
   ```

## RBAC System

The application includes a comprehensive RBAC system that integrates with QBraid organizations:

### Organization Roles

- **`owner`** - Full organization control and administrative access
- **`admin`** - Administrative access within organization
- **`member`** - Standard organization member access
- **`viewer`** - Read-only access to organization resources

### Permission System

Roles are automatically mapped to granular permissions:

- `view:devices`, `manage:devices`
- `view:profile`, `edit:profile`
- `view:team`, `manage:team`
- `view:earnings`, `manage:earnings`
- `view:jobs`, `manage:jobs`
- `admin:access`

### Usage Examples

**Server Actions:**

```typescript
import { requirePermission } from '@/lib/rbac';
import { Permission } from '@/types/auth';

export async function deleteDevice(deviceId: string) {
  await requirePermission(Permission.ManageDevices);
  // Your logic here
}
```

**Client Components:**

```tsx
import { usePermissions, PermissionGuard } from '@/hooks/use-permissions';

function DeviceManagement() {
  const { hasPermission } = usePermissions();

  return (
    <PermissionGuard permission={Permission.ViewDevices}>
      <DeviceList />
    </PermissionGuard>
  );
}
```

## Documentation

- [Complete RBAC Implementation Guide](./docs/RBAC_IMPLEMENTATION.md)
- [TanStack Query Integration](./docs/TANSTACK_QUERY_INTEGRATION.md)

## Architecture

- **Frontend**: Next.js 14 with App Router
- **Authentication**: AWS Cognito with custom session management
- **State Management**: TanStack Query with Redis caching
- **Authorization**: Role-based permissions with QBraid API integration
- **Database**: Redis for session storage and caching
- **Styling**: Tailwind CSS with shadcn/ui components

## API Integration

The RBAC system automatically fetches user roles from your existing QBraid organizations API:

```typescript
// Automatically called during authentication
GET /orgs/get/0/50

// Response includes user roles per organization
{
  "organizations": [
    {
      "org": {
        "role": "admin",
        "email": "<EMAIL>",
        "organization": { "name": "Research Lab" }
      }
    }
  ]
}
```

## Development

```bash
# Run development server
npm run dev

# Run tests
npm test

# Run linting
npm run lint

# Build for production
npm run build
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

[Your License Here]
