const { defineConfig } = require('cypress');

// require('dotenv').config();

module.exports = defineConfig({
  e2e: {
    // supportFile: 'cypress/support/auth-provider-commands/cognito.js',
    baseUrl: 'http://localhost:3000',
  },
  video: false,
  env: {
    cognito_username: process.env.AWS_COGNITO_USERNAME,
    cognito_password: process.env.AWS_COGNITO_PASSWORD,
    CYPRESS_RECORD_KEY: '00e47197-cccb-4a52-a408-bb29be80c778',
    CYPRESS_VERIFY_TIMEOUT: 120000,
    X_URL: 'https://x.com/qbraid_official',
    DISCORD_URL: 'https://discord.gg/9jpmpeEV65',
    LINKED_IN_URL: 'https://www.linkedin.com/company/qbraid-official/',
    GIT_HUB_URL: 'https://github.com/qbraid',
    DOCS_URL: 'https://docs.qbraid.com/lab/user-guide/overview',
    FOOTER_WORK_WITH_US_URL: 'https://qbraid.com/careers/',
    TEST_USER_EMAIL: '<EMAIL>',
    TEST_USER_PASSWORD: 'qBraid2!',
    TEST_USER_COOKIE_EMAIL: 'markovshama%40gmail.com',
    TEST_ENV: 'qbraid_sdk_9j9sjy',
    TEST_GITHUB_URL: 'https://github.com/qBraid/qBraid.git',
    // api to use inside cypress requests
    // local env
    local: {
      API_BASE_URL: 'http://localhost:3001/api',
    },
    // github action env
    ci: {
      API_BASE_URL: 'https://api-staging-1.qbraid.com/api',
    },
  },

  component: {
    devServer: {
      framework: 'create-react-app',
      bundler: 'webpack',
    },
  },
});
