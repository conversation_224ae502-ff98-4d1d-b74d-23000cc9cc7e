'use client';

import React from 'react';
import { Permission } from '@/types/auth';
import { useOrgPermissions } from '@/hooks/use-permissions';
import { useOrgContext } from '@/components/org/org-context-provider';
import { OrgPermissionGuard, useOrgPermissionCheck, useIsOrgAdmin } from '@/components/auth/org-permission-guard';

/**
 * Demo component to showcase organization-specific permissions
 * This demonstrates how permissions change based on organization context
 */
export function OrgPermissionsDemo() {
  const { currentOrgId, organizations, switchOrganization, isLoading: orgLoading } = useOrgContext();
  const { checkAccess, currentOrgRole, isOrgSpecific } = useOrgPermissionCheck();
  const isAdmin = useIsOrgAdmin();

  // Get permissions for current organization
  const {
    roles,
    permissions,
    orgRoles,
    orgContext,
    isLoading,
    hasError,
    source,
    cached,
  } = useOrgPermissions();

  if (orgLoading || isLoading) {
    return (
      <div className="p-6 bg-gray-50 rounded-lg">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-300 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-300 rounded w-1/2 mb-2"></div>
          <div className="h-4 bg-gray-300 rounded w-3/4"></div>
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <h3 className="text-red-800 font-semibold mb-2">Permission Error</h3>
        <p className="text-red-600">Failed to load organization permissions.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Organization Context Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-blue-900 mb-4">
          🏢 Organization Context
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-blue-700 mb-1">
              Current Organization
            </label>
            <p className="text-blue-900 font-mono">
              {currentOrgId || 'None selected'}
            </p>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-blue-700 mb-1">
              Your Role
            </label>
            <p className="text-blue-900 font-mono">
              {currentOrgRole || 'No role'}
            </p>
          </div>
        </div>

        {/* Organization Switcher */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-blue-700 mb-2">
            Switch Organization
          </label>
          <div className="flex flex-wrap gap-2">
            {organizations.map((org) => (
              <button
                key={org.orgId}
                onClick={() => switchOrganization(org.orgId)}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  currentOrgId === org.orgId
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-blue-600 border border-blue-300 hover:bg-blue-50'
                }`}
              >
                {org.orgName} ({org.role})
              </button>
            ))}
          </div>
        </div>

        {/* Permission Metadata */}
        <div className="text-xs text-blue-600 space-y-1">
          <p>Source: {source} | Cached: {cached ? 'Yes' : 'No'} | Org-Specific: {isOrgSpecific ? 'Yes' : 'No'}</p>
          {orgContext && (
            <p>Scope: {orgContext.scope} | Requested: {orgContext.requestedOrgId || 'global'}</p>
          )}
        </div>
      </div>

      {/* Current Permissions */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-green-900 mb-4">
          🔑 Current Permissions
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-green-800 mb-2">Roles ({roles.length})</h4>
            <div className="space-y-1">
              {roles.map((role) => (
                <span
                  key={role}
                  className="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded mr-2"
                >
                  {role}
                </span>
              ))}
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-green-800 mb-2">Permissions ({permissions.length})</h4>
            <div className="max-h-32 overflow-y-auto space-y-1">
              {permissions.map((permission) => (
                <div
                  key={permission}
                  className="text-xs text-green-700 font-mono"
                >
                  {permission}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Permission Guards Demo */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-purple-900 mb-4">
          🛡️ Permission Guards Demo
        </h3>
        
        <div className="space-y-4">
          {/* Admin Access */}
          <div className="border border-purple-200 rounded p-4">
            <h4 className="font-medium text-purple-800 mb-2">Admin Access</h4>
            <OrgPermissionGuard
              role={['admin', 'owner']}
              fallback={
                <div className="text-red-600 text-sm">
                  ❌ Access denied - Admin or Owner role required
                </div>
              }
            >
              <div className="text-green-600 text-sm">
                ✅ Admin panel access granted! You are: {isAdmin ? 'Admin' : 'Not Admin'}
              </div>
            </OrgPermissionGuard>
          </div>

          {/* Device Management */}
          <div className="border border-purple-200 rounded p-4">
            <h4 className="font-medium text-purple-800 mb-2">Device Management</h4>
            <OrgPermissionGuard
              permission={Permission.ManageDevices}
              fallback={
                <div className="text-red-600 text-sm">
                  ❌ Cannot manage devices - Permission required: {Permission.ManageDevices}
                </div>
              }
            >
              <div className="text-green-600 text-sm">
                ✅ Device management access granted!
              </div>
            </OrgPermissionGuard>
          </div>

          {/* Team Management */}
          <div className="border border-purple-200 rounded p-4">
            <h4 className="font-medium text-purple-800 mb-2">Team Management</h4>
            <OrgPermissionGuard
              permission={[Permission.ViewTeam, Permission.ManageTeam]}
              requireAll={true}
              fallback={
                <div className="text-red-600 text-sm">
                  ❌ Cannot manage team - Both view and manage permissions required
                </div>
              }
            >
              <div className="text-green-600 text-sm">
                ✅ Full team management access granted!
              </div>
            </OrgPermissionGuard>
          </div>

          {/* Earnings Access */}
          <div className="border border-purple-200 rounded p-4">
            <h4 className="font-medium text-purple-800 mb-2">Earnings Access</h4>
            <OrgPermissionGuard
              permission={Permission.ViewEarnings}
              fallback={
                <div className="text-red-600 text-sm">
                  ❌ Cannot view earnings - Permission required: {Permission.ViewEarnings}
                </div>
              }
            >
              <div className="text-green-600 text-sm">
                ✅ Earnings data access granted!
              </div>
            </OrgPermissionGuard>
          </div>
        </div>
      </div>

      {/* Imperative Permission Checks */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-yellow-900 mb-4">
          🔍 Imperative Permission Checks
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-medium text-yellow-800 mb-2">Permission Checks</h4>
            <ul className="space-y-1">
              <li>
                Can view devices: {checkAccess(Permission.ViewDevices) ? '✅' : '❌'}
              </li>
              <li>
                Can manage devices: {checkAccess(Permission.ManageDevices) ? '✅' : '❌'}
              </li>
              <li>
                Can view earnings: {checkAccess(Permission.ViewEarnings) ? '✅' : '❌'}
              </li>
              <li>
                Can manage earnings: {checkAccess(Permission.ManageEarnings) ? '✅' : '❌'}
              </li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium text-yellow-800 mb-2">Role Checks</h4>
            <ul className="space-y-1">
              <li>
                Is admin: {checkAccess(undefined, 'admin') ? '✅' : '❌'}
              </li>
              <li>
                Is owner: {checkAccess(undefined, 'owner') ? '✅' : '❌'}
              </li>
              <li>
                Is member: {checkAccess(undefined, 'member') ? '✅' : '❌'}
              </li>
              <li>
                Is viewer: {checkAccess(undefined, 'viewer') ? '✅' : '❌'}
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* All Organization Roles */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          📋 All Organization Roles
        </h3>
        
        <div className="space-y-2">
          {Object.entries(orgRoles).map(([orgId, orgRole]) => (
            <div
              key={orgId}
              className={`p-3 rounded border ${
                currentOrgId === orgId
                  ? 'border-blue-300 bg-blue-50'
                  : 'border-gray-200 bg-white'
              }`}
            >
              <div className="flex justify-between items-center">
                <div>
                  <span className="font-medium">{orgRole.orgName}</span>
                  <span className="ml-2 text-sm text-gray-500">({orgRole.role})</span>
                </div>
                <div className="text-xs text-gray-400">
                  {orgId.substring(0, 8)}...
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
