/**
 * DeviceJobsTab component
 * -----------------------
 * This module defines the Jobs Info tab for the device card.
 * It fetches and displays a summary and table of quantum jobs for a device,
 * including charts, job statistics, and a searchable jobs table.
 * Individual job rows are rendered using the JobsRow component.
 *
 * All API fetching and state management for jobs data is handled here.
 * The functionality on this tab is different from all other tabs, which is why it is separate.
 */

'use client';
import { useState, memo, useMemo } from 'react';
import { Search, Filter, ChevronDown, Download } from 'lucide-react';
import { PieChart, Pie, Cell, Tooltip, ResponsiveContainer } from 'recharts';
import { Card, CardContent } from '@/components/ui/card';
import { TabsContent } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { JobsTableRow } from '@/components/jobs-row';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { PercentBar } from '@/components/percent-bar';
import { useJobsForDevice } from '@/hooks/use-api';
import type { JobsRowProps, JobsDisplayProps } from '@/types/jobs';

const JOB_TYPE_COLORS: Record<string, string> = {
  gate_model: '#8a2be2',
  ahs: '#ef4444',
  simulation: '#22c55e',
  hybrid: '#eab308',
  annealing: '#06b6d4',
  photonic: '#f59e42',
  other: '#64748b',
  unknown: '#94a3b8',
};

const jobStatuses = ['Completed', 'Running', 'Queued', 'Unsuccessful'];

// Computes job statistics from the jobsArray and displays them using PercentBar components.
const JobsByStatus = memo(({ jobsArray }: { jobsArray: JobsRowProps[] }) => {
  const { totalJobs, completedJobs, runningJobs, queuedJobs, unsuccessfulJobs } = jobsArray.reduce(
    (acc, job) => {
      if (job.qbraidStatus === 'COMPLETED') acc.completedJobs++;
      else if (job.qbraidStatus === 'RUNNING') acc.runningJobs++;
      else if (job.qbraidStatus === 'QUEUED') acc.queuedJobs++;
      else if (job.qbraidStatus === 'FAILED' || job.qbraidStatus === 'CANCELLED')
        acc.unsuccessfulJobs++;
      acc.totalJobs++;
      return acc;
    },
    { totalJobs: 0, completedJobs: 0, runningJobs: 0, queuedJobs: 0, unsuccessfulJobs: 0 },
  );

  const completedPercentage: number = totalJobs > 0 ? (completedJobs / totalJobs) * 100 : 0;
  const runningPercentage: number = totalJobs > 0 ? (runningJobs / totalJobs) * 100 : 0;
  const queuedPercentage: number = totalJobs > 0 ? (queuedJobs / totalJobs) * 100 : 0;
  const unsuccessfulPercentage: number = totalJobs > 0 ? (unsuccessfulJobs / totalJobs) * 100 : 0;

  return (
    <div className="w-full max-w-[500px] lg:max-w-full">
      <h4 className="text-lg font-semibold mb-4">Jobs by Status</h4>
      <div className="space-y-3">
        <PercentBar
          name="Completed"
          percentage={`${completedPercentage.toFixed(2)}%`}
          color="bg-[#22c55e]"
        />
        <PercentBar
          name="Running"
          percentage={`${runningPercentage.toFixed(2)}%`}
          color="bg-[#3b82f6]"
        />
        <PercentBar
          name="Queued"
          percentage={`${queuedPercentage.toFixed(2)}%`}
          color="bg-[#eab308]"
        />
        <PercentBar
          name="Unsuccessful"
          percentage={`${unsuccessfulPercentage.toFixed(2)}%`}
          color="bg-[#94a3b8]"
        />
      </div>
    </div>
  );
});

function getJobTypeDistribution(jobsArray: JobsRowProps[]) {
  const typeCounts: Record<string, number> = {};
  jobsArray.forEach((job) => {
    const type = job.experimentType || 'unknown';
    typeCounts[type] = (typeCounts[type] || 0) + 1;
  });
  const total = jobsArray.length;
  return Object.entries(typeCounts).map(([type, count]) => ({
    name: type,
    value: count,
    percent: total > 0 ? ((count / total) * 100).toFixed(2) : '0.00',
  }));
}

function getAverageCost(jobsArray: JobsRowProps[]) {
  let totalCost: number = 0;
  let numJobsWithCost: number = 0;
  jobsArray.forEach((job) => {
    if (job.cost) {
      totalCost += job.cost;
      numJobsWithCost++;
    }
  });
  if (numJobsWithCost == 0) {
    return 0;
  }
  return totalCost / numJobsWithCost;
}

function getAverageShots(jobsArray: JobsRowProps[]) {
  let totalShots: number = 0;
  let numJobsWithShots: number = 0;
  jobsArray.forEach((job) => {
    if (job.shots) {
      totalShots += job.shots;
      numJobsWithShots++;
    }
  });
  if (numJobsWithShots == 0) {
    return 0;
  }
  return totalShots / numJobsWithShots;
}

export function DeviceJobsTab() {
  // Fetch jobs using the React Query API hook instead of useEffect
  // TODO: Make provider and device dynamic as needed
  const provider = 'qbraid';
  const device = 'qbraid_qir_simulator';
  const [jobsFilter, setJobsFilter] = useState<string>('All Statuses');
  const [jobsSearch, setJobsSearch] = useState<string>('');
  const [page, setPage] = useState(0);
  const [resultsPerPage, setResultsPerPage] = useState(10);
  const { data, isLoading } = useJobsForDevice(provider, device, page, resultsPerPage);
  const jobsInfo = data || { jobsArray: [{ qbraidDeviceId: 'null-device' }] };
  const totalJobs = data?.total || jobsInfo.jobsArray.length;

  const jobTypeData = useMemo(
    () => getJobTypeDistribution(jobsInfo.jobsArray),
    [jobsInfo.jobsArray],
  );
  const averageCost: number = useMemo(
    () => getAverageCost(jobsInfo.jobsArray),
    [jobsInfo.jobsArray],
  );
  const averageShots: number = useMemo(
    () => getAverageShots(jobsInfo.jobsArray),
    [jobsInfo.jobsArray],
  );

  const totalPages = Math.ceil(totalJobs / resultsPerPage);

  function isStatusMatch(status?: string, filter?: string) {
    let result =
      status?.toLowerCase() === filter?.toLowerCase() ||
      (filter === 'Unsuccessful' &&
        (status?.toLowerCase() === 'failed' || status?.toLowerCase() === 'cancelled'));
    return result;
  }

  function isSearchMatch(job: JobsRowProps, search: string) {
    const lowerSearch = search.toLowerCase();
    let result =
      job.vendor?.toLowerCase().includes(lowerSearch) ||
      job.provider?.toLowerCase().includes(lowerSearch) ||
      job.experimentType?.toLowerCase().includes(lowerSearch) ||
      job.shots?.toString().includes(lowerSearch) ||
      job.cost?.toString().includes(lowerSearch) ||
      job.timeStamps?.createdAt?.toLocaleString().toLowerCase().includes(lowerSearch) ||
      job.timeStamps?.endedAt?.toLocaleString().toLowerCase().includes(lowerSearch);
    return result;
  }

  const filteredJobs = useMemo(
    () =>
      jobsInfo.jobsArray.filter((job: JobsRowProps) => {
        if (job.qbraidDeviceId === 'No jobs found' || job.qbraidDeviceId === 'null-device') {
          return false; // Exclude the empty job placeholder
        }
        const matchesStatus =
          jobsFilter === 'All Statuses' || isStatusMatch(job.qbraidStatus, jobsFilter);

        const matchesSearch = jobsSearch.trim() === '' || isSearchMatch(job, jobsSearch);

        return matchesStatus && matchesSearch;
      }),
    [jobsInfo.jobsArray, jobsFilter, jobsSearch],
  );

  // The content for the Jobs Info tab on the quantum device card.
  return (
    <TabsContent value="jobs-info" className="mt-6">
      <div>
        <h3 className="text-xl font-semibold mb-2">Jobs Overview</h3>
        <p className="text-[#94a3b8] text-sm mb-6">Summary of your quantum computing jobs</p>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Jobs by Type chart*/}
          <div className="w-full max-w-[500px] lg:max-w-full">
            <h4 className="text-lg font-semibold mb-4">Jobs by Type</h4>
            <div className="grid grid-cols-2 mb-8">
              <div className="relative w-48 h-40 mx-auto">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={jobTypeData}
                      dataKey="value"
                      nameKey="name"
                      cx="60%"
                      cy="50%"
                      outerRadius={70}
                      fill="#8884d8"
                    >
                      {jobTypeData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={JOB_TYPE_COLORS[entry.name] || '#94a3b8'}
                        />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value: number, name: string, props: any) => [
                        `${value}`,
                        `${props.payload.name}`,
                      ]}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              {/* Legend */}
              <div className="mt-4 space-y-2 text-sm">
                {jobTypeData.map((entry, idx) => (
                  <div key={entry.name} className="flex items-center space-x-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: JOB_TYPE_COLORS[entry.name] || '#8884d8' }}
                    ></div>
                    <span className="text-[#94a3b8]">
                      {entry.name} ({entry.percent}%)
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Jobs by Status chart */}
          <JobsByStatus jobsArray={jobsInfo.jobsArray} />
        </div>

        {/* Average Queue and Execution Times */}
        <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <p className="text-[#94a3b8] text-md">Average Cost per Task (Credits)</p>
            <p className="text-white text-md font-semibold">{averageCost.toFixed(4)}</p>
          </div>
          <div>
            <p className="text-[#94a3b8] text-md">Average Shots per Task</p>
            <p className="text-white text-md font-semibold">{averageShots.toFixed(4)}</p>
          </div>
          <div>
            <p className="text-[#94a3b8] text-md">Total Number of Tasks</p>
            <p className="text-white text-md font-semibold">{totalJobs.toFixed(0)}</p>
          </div>
        </div>

        {/* List of jobs */}
        <div className="flex space-y-4 flex-col mt-4">
          <div className="flex items-center justify-between">
            <h4 id="view-jobs" className="text-lg font-semibold mt-2">
              View jobs
            </h4>
          </div>

          {/* Search bar */}
          {/* See issue https://github.com/awong08/partner-dashboard/issues/13 */}
          <div className="flex items-center space-x-4 mb-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#94a3b8] w-4 h-4" />
              <Input
                placeholder="Search jobs..."
                value={jobsSearch}
                onChange={(e) => setJobsSearch(e.target.value)}
                className="pl-10 bg-[#262131] border-[#3b3b3b] text-white placeholder:text-[#94a3b8]"
              />
            </div>

            {/* Filter by Status dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  aria-label="Filter by Status"
                  variant="outline"
                  className="bg-[#262131] border-[#3b3b3b] text-[#94a3b8] hover:bg-[#3b3b3b] hover:text-white"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  {jobsFilter}
                  <ChevronDown className="w-4 h-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-[#262131] border-[#3b3b3b]">
                <DropdownMenuItem
                  className="text-[#94a3b8] hover:text-white hover:bg-[#3b3b3b]"
                  onClick={() => setJobsFilter('All Statuses')}
                >
                  All Statuses
                </DropdownMenuItem>
                {jobStatuses.map((status) => (
                  <DropdownMenuItem
                    key={status}
                    className="text-[#94a3b8] hover:text-white hover:bg-[#3b3b3b]"
                    onClick={() => setJobsFilter(status)}
                  >
                    {status}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Download Action Log Button */}
            <Button
              aria-label="Download Action Log"
              variant="outline"
              size="sm"
              className="bg-[#262131] border-[#3b3b3b] text-[#94a3b8] hover:bg-[#3b3b3b] hover:text-white"
            >
              <Download className="w-4 h-4" />
            </Button>
          </div>

          {/* Pagination Controls */}
          <div className="flex items-center mb-4 space-x-8">
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="px-3 py-1 rounded bg-transparent border-[#3b3b3b] text-white hover:bg-[#3b3b3b] hover:text-white"
                onClick={() => {
                  setPage((p) => Math.max(0, p - 1));
                  document.getElementById('view-jobs')?.scrollIntoView({ behavior: 'smooth' });
                }}
                disabled={page === 0}
              >
                Previous
              </Button>
              <span className="text-[#94a3b8]">
                Page {page + 1} of {totalPages || 1}
              </span>
              <Button
                variant="outline"
                className="px-3 py-1 rounded bg-transparent border-[#3b3b3b] text-white hover:bg-[#3b3b3b] hover:text-white"
                onClick={() => {
                  setPage((p) => Math.min(totalPages - 1, p + 1));
                  document.getElementById('view-jobs')?.scrollIntoView({ behavior: 'smooth' });
                }}
                disabled={page >= totalPages - 1}
              >
                Next
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-[#94a3b8]">Rows per page:</span>
              <select
                className="px-2 py-1 rounded bg-[#262131] border-[#3b3b3b] text-white hover:bg-[#3b3b3b]"
                value={resultsPerPage}
                onChange={(e) => {
                  setResultsPerPage(Number(e.target.value));
                  setPage(0);
                  document.getElementById('view-jobs')?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                {[5, 10, 20, 50].map((opt) => (
                  <option key={opt} value={opt}>
                    {opt}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Jobs Table */}
          <Card className="bg-[#262131] border-[#3b3b3b]">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  {/* Jobs table header */}
                  <thead>
                    <tr className="border-b border-[#3b3b3b]">
                      <th className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm">
                        Device ID (qBraid)
                      </th>
                      <th className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm">
                        Created At / Ended At
                      </th>
                      <th className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm">
                        Queue<br></br>Position
                      </th>
                      <th className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm">
                        Job Status
                      </th>
                      <th className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm">
                        Vendor
                      </th>
                      <th className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm">
                        Provider
                      </th>
                      <th className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm">
                        Shots
                      </th>
                      <th className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm">
                        Cost (Credits)
                      </th>
                      <th className="text-left py-3 px-4 text-[#94a3b8] font-medium text-sm">
                        Experiment Type
                      </th>
                    </tr>
                  </thead>
                  {/* Jobs table body */}
                  <tbody>
                    {isLoading ? (
                      <tr>
                        <td colSpan={10} className="text-center py-6 text-[#94a3b8]">
                          Loading...
                        </td>
                      </tr>
                    ) : filteredJobs.length === 0 ? (
                      <tr>
                        <td colSpan={10} className="text-center py-6 text-[#94a3b8]">
                          No jobs found.
                        </td>
                      </tr>
                    ) : (
                      filteredJobs.map((job: JobsRowProps) => (
                        <JobsTableRow
                          key={job.qbraidJobId || job.qbraidDeviceId || Math.random()}
                          {...job}
                        />
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </TabsContent>
  );
}
