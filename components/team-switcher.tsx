'use client';

import * as React from 'react';
import { ChevronsUpDown, Plus, Loader2 } from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar';

import { usePermissions } from '@/hooks/use-permissions';
import { useOrgContext } from '@/components/org/org-context-provider';

export function TeamSwitcher() {
  const { isMobile } = useSidebar();
  const { loading: permissionsLoading } = usePermissions();
  const { currentOrg, organizations, switchOrganization, isLoading: orgLoading } = useOrgContext();

  const loading = permissionsLoading || orgLoading;
  const activeOrg = currentOrg || organizations[0];

  // Handle organization switching
  const handleOrgSwitch = async (orgId: string) => {
    if (orgId === currentOrg?.orgId) return; // No change needed

    try {
      await switchOrganization(orgId);
      console.log(`🔄 [TEAM-SWITCHER] Successfully switched to org:`, {
        orgId,
        orgName: organizations.find((org) => org.orgId === orgId)?.orgName,
      });
    } catch (error) {
      console.error(`❌ [TEAM-SWITCHER] Failed to switch organization:`, error);
    }
  };

  console.log(`🏢 [TEAM-SWITCHER] Rendering with:`, {
    organizationsCount: organizations.length,
    currentOrgId: currentOrg?.orgId || null,
    activeOrgName: activeOrg?.orgName || 'none',
    activeOrgRole: activeOrg?.role || 'none',
  });

  if (loading) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg" className="animate-pulse">
            <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-gray-200" />
            <div className="grid flex-1 text-left text-sm leading-tight">
              <div className="h-4 bg-gray-200 rounded w-24" />
              <div className="h-3 bg-gray-200 rounded w-16 mt-1" />
            </div>
            <ChevronsUpDown className="ml-auto size-4" />
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }

  if (organizations.length === 0) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg">
            <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
              <span className="text-sm font-semibold">?</span>
            </div>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-semibold">No Organization</span>
              <span className="truncate text-xs">Not assigned</span>
            </div>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                <span className="text-sm font-semibold">
                  {activeOrg?.orgName?.charAt(0).toUpperCase() || 'O'}
                </span>
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">
                  {activeOrg?.orgName || 'Select Organization'}
                </span>
                <span className="truncate text-xs">
                  {activeOrg
                    ? `${activeOrg.role} • ${organizations.length} org${organizations.length !== 1 ? 's' : ''}`
                    : 'No access'}
                </span>
              </div>
              {orgLoading ? (
                <Loader2 className="ml-auto size-4 animate-spin" />
              ) : (
                <ChevronsUpDown className="ml-auto size-4" />
              )}
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            align="start"
            side={isMobile ? 'bottom' : 'right'}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-xs text-muted-foreground">
              Organizations ({organizations.length})
            </DropdownMenuLabel>
            {organizations.map((org) => (
              <DropdownMenuItem
                key={org.orgId}
                onClick={() => handleOrgSwitch(org.orgId)}
                className="gap-2 p-2"
                disabled={orgLoading}
              >
                <div className="flex size-6 items-center justify-center rounded-sm bg-sidebar-primary text-sidebar-primary-foreground text-xs font-medium">
                  {org.orgName.charAt(0).toUpperCase()}
                </div>
                <div className="flex flex-col flex-1">
                  <span className="font-medium">{org.orgName}</span>
                  <span className="text-xs text-muted-foreground">
                    {org.role} • Updated {new Date(org.updated).toLocaleDateString()}
                  </span>
                </div>
                {org.orgId === activeOrg?.orgId && <DropdownMenuShortcut>✓</DropdownMenuShortcut>}
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            <DropdownMenuItem className="gap-2 p-2">
              <div className="flex size-6 items-center justify-center rounded-md border bg-background">
                <Plus className="size-4" />
              </div>
              <div className="font-medium text-muted-foreground">Add organization</div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
