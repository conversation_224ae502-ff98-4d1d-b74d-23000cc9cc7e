/*
  DeviceStatsCards
  ----------------
  Displays key aggregate metrics for devices: total devices, online, offline, and servers.
  Uses shadcn Card components for consistent styling.
*/
'use client';

import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { WifiIcon, WifiOffIcon, Layers3Icon } from 'lucide-react';

interface DeviceStatsCardsProps {
  total: number;
  online: number;
  offline: number;
  servers: number;
}

export function DeviceStatsCards({ total, online, offline, servers }: DeviceStatsCardsProps) {
  const cardBaseClass =
    'bg-gradient-to-t from-sidebar to-sidebar/80 @container/card hover:border-[#4b5563] transition-all duration-300 border border-sidebar-border rounded-xl';

  const renderCard = (label: string, value: number, icon: React.ReactElement) => (
    <Card className={cardBaseClass}>
      <CardHeader className="relative pb-4">
        <CardDescription>{label}</CardDescription>
        <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
          {value}
        </CardTitle>
        <div className="absolute right-4 top-4">{icon}</div>
      </CardHeader>
    </Card>
  );

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
      {renderCard(
        'Total Devices',
        total,
        <Layers3Icon className="size-5" style={{ color: '#8a2be2' }} />,
      )}
      {renderCard('Online', online, <WifiIcon className="size-5" style={{ color: '#22c55e' }} />)}
      {renderCard(
        'Offline',
        offline,
        <WifiOffIcon className="size-5" style={{ color: '#ef4444' }} />,
      )}
    </div>
  );
}
