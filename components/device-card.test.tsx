import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { DeviceCard } from './device-card';

const mockProps = {
  qbraid_id: 'dev-123',
  name: 'Test Quantum Device',
  type: 'Superconducting',
  deviceDescription: 'A test quantum device for unit testing.',
  paradigm: 'Gate-based',
  numberQubits: 5,
  status: 'active',
  provider: 'TestProvider',
  architecture: 'N/A',
  processorType: 'N/A',
  vendor: 'TestVendor',
  runPackage: 'test-package',
};

describe('DeviceCard', () => {
  it('renders device name, type badge, and Details tab', () => {
    render(
      <DeviceCard
        pricing={{
          perMinute: '',
          perTask: '',
          perShot: '',
        }}
        pendingJobs={0}
        noiseModels={[]}
        {...{
          ...mockProps,
          numberQubits: String(mockProps.numberQubits), // Fix type: string instead of number
        }}
      />,
    );
    expect(screen.getByText('Test Quantum Device')).toBeInTheDocument();
    expect(screen.getByText('Superconducting')).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: /details/i })).toBeInTheDocument();
  });
});
