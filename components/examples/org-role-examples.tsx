'use client';

import React from 'react';
import { OrgRoleGuard, useOrg<PERSON>ole, RoleCheck } from '@/components/auth/org-role-guard';
import { OrgInfo, OrgSelector } from '@/components/org/org-context-provider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Shield, Users, Settings, Eye, Crown, UserCheck } from 'lucide-react';

/**
 * Examples showcasing the new organization-aware role checking system
 */
export function OrgRoleExamples() {
  const { role, organization, isAdmin, canManage, canView, loading } = useOrgRole();

  if (loading) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-4 bg-gray-200 rounded w-3/4" />
        <div className="h-4 bg-gray-200 rounded w-1/2" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Current Organization Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Current Organization Context
          </CardTitle>
          <CardDescription>
            Your current organization and role information
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <OrgInfo showRole showCount className="text-lg" />
            <OrgSelector compact />
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="font-semibold text-sm text-gray-600">Your Role</div>
              <Badge variant={isAdmin() ? 'default' : 'secondary'} className="mt-1">
                {role || 'none'}
              </Badge>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="font-semibold text-sm text-gray-600">Can Manage</div>
              <Badge variant={canManage() ? 'default' : 'outline'} className="mt-1">
                {canManage() ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="font-semibold text-sm text-gray-600">Can View</div>
              <Badge variant={canView() ? 'default' : 'outline'} className="mt-1">
                {canView() ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="font-semibold text-sm text-gray-600">Is Admin</div>
              <Badge variant={isAdmin() ? 'default' : 'outline'} className="mt-1">
                {isAdmin() ? 'Yes' : 'No'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Role-Based Component Examples */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* Admin Only Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Crown className="h-5 w-5 text-yellow-500" />
              Admin/Owner Only
            </CardTitle>
            <CardDescription>
              Content visible only to admins and owners
            </CardDescription>
          </CardHeader>
          <CardContent>
            <OrgRoleGuard 
              requiredRoles={['admin', 'owner']} 
              showRoleInfo
              fallback={
                <div className="text-center py-8 text-gray-500">
                  <Crown className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>Admin or Owner access required</p>
                </div>
              }
            >
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-green-600">
                  <UserCheck className="h-4 w-4" />
                  <span className="font-medium">Admin Dashboard Access</span>
                </div>
                <Button className="w-full" variant="default">
                  <Settings className="h-4 w-4 mr-2" />
                  Organization Settings
                </Button>
                <Button className="w-full" variant="outline">
                  <Users className="h-4 w-4 mr-2" />
                  Manage Team Members
                </Button>
              </div>
            </OrgRoleGuard>
          </CardContent>
        </Card>

        {/* Member+ Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-500" />
              Member+ Access
            </CardTitle>
            <CardDescription>
              Content for members, admins, and owners
            </CardDescription>
          </CardHeader>
          <CardContent>
            <OrgRoleGuard 
              requiredRoles={['member', 'admin', 'owner']} 
              showRoleInfo
              fallback={
                <div className="text-center py-8 text-gray-500">
                  <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>Member access required</p>
                </div>
              }
            >
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-blue-600">
                  <UserCheck className="h-4 w-4" />
                  <span className="font-medium">Team Member Features</span>
                </div>
                <Button className="w-full" variant="default">
                  View Team Resources
                </Button>
                <Button className="w-full" variant="outline">
                  Submit Reports
                </Button>
              </div>
            </OrgRoleGuard>
          </CardContent>
        </Card>

        {/* Viewer+ Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5 text-gray-500" />
              Viewer+ Access
            </CardTitle>
            <CardDescription>
              Content for all organization members
            </CardDescription>
          </CardHeader>
          <CardContent>
            <OrgRoleGuard 
              requiredRoles={['viewer', 'member', 'admin', 'owner']} 
              showRoleInfo
              fallback={
                <div className="text-center py-8 text-gray-500">
                  <Eye className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>Organization access required</p>
                </div>
              }
            >
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-gray-600">
                  <UserCheck className="h-4 w-4" />
                  <span className="font-medium">Basic Organization Access</span>
                </div>
                <Button className="w-full" variant="default">
                  View Organization Info
                </Button>
                <Button className="w-full" variant="outline">
                  Read Documentation
                </Button>
              </div>
            </OrgRoleGuard>
          </CardContent>
        </Card>

        {/* Conditional Rendering Example */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-purple-500" />
              Conditional Rendering
            </CardTitle>
            <CardDescription>
              Using RoleCheck for conditional logic
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <RoleCheck roles={['admin', 'owner']}>
                {(hasAccess, userRole, org) => (
                  <div className={`p-3 rounded-lg ${hasAccess ? 'bg-green-50 border border-green-200' : 'bg-gray-50 border border-gray-200'}`}>
                    <div className="font-medium text-sm">
                      {hasAccess ? '✅ Admin Features Available' : '❌ Admin Features Locked'}
                    </div>
                    <div className="text-xs text-gray-600 mt-1">
                      Role: {userRole || 'none'} | Org: {org?.orgName || 'none'}
                    </div>
                  </div>
                )}
              </RoleCheck>

              <RoleCheck role="member">
                {(hasAccess) => (
                  <Button 
                    className="w-full" 
                    variant={hasAccess ? "default" : "outline"}
                    disabled={!hasAccess}
                  >
                    {hasAccess ? 'Access Member Portal' : 'Member Access Required'}
                  </Button>
                )}
              </RoleCheck>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Info */}
      <Card>
        <CardHeader>
          <CardTitle>🚀 Performance Benefits</CardTitle>
          <CardDescription>
            Advantages of the new organization-aware role checking system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-3 gap-4 text-sm">
            <div className="p-3 bg-blue-50 rounded-lg">
              <div className="font-medium text-blue-800">Cached Role Data</div>
              <div className="text-blue-600 mt-1">
                Roles are cached and reused across components, reducing API calls
              </div>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <div className="font-medium text-green-800">Organization Context</div>
              <div className="text-green-600 mt-1">
                Automatic organization switching with persistent state
              </div>
            </div>
            <div className="p-3 bg-purple-50 rounded-lg">
              <div className="font-medium text-purple-800">Better UX</div>
              <div className="text-purple-600 mt-1">
                Clear loading states and role information for users
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
