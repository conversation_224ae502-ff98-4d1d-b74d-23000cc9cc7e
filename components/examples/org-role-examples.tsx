'use client';

import React from 'react';
import { OrgRoleGuard, useOrg<PERSON><PERSON>, RoleCheck } from '@/components/auth/org-role-guard';
import { OrgInfo, OrgSelector } from '@/components/org/org-context-provider';
import { useEnhancedOrgRole, useUserOrgRole, usePermissions } from '@/hooks/use-permissions';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Shield, Users, Settings, Eye, Crown, UserCheck, Database, Zap } from 'lucide-react';

/**
 * Examples showcasing the new organization-aware role checking system
 */
export function OrgRoleExamples() {
  const { role, organization, isAdmin, canManage, canView, loading } = useOrgRole();

  if (loading) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-4 bg-gray-200 rounded w-3/4" />
        <div className="h-4 bg-gray-200 rounded w-1/2" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Current Organization Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Current Organization Context
          </CardTitle>
          <CardDescription>Your current organization and role information</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <OrgInfo showRole showCount className="text-lg" />
            <OrgSelector compact />
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="font-semibold text-sm text-gray-600">Your Role</div>
              <Badge variant={isAdmin() ? 'default' : 'secondary'} className="mt-1">
                {role || 'none'}
              </Badge>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="font-semibold text-sm text-gray-600">Can Manage</div>
              <Badge variant={canManage() ? 'default' : 'outline'} className="mt-1">
                {canManage() ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="font-semibold text-sm text-gray-600">Can View</div>
              <Badge variant={canView() ? 'default' : 'outline'} className="mt-1">
                {canView() ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="font-semibold text-sm text-gray-600">Is Admin</div>
              <Badge variant={isAdmin() ? 'default' : 'outline'} className="mt-1">
                {isAdmin() ? 'Yes' : 'No'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Role-Based Component Examples */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* Admin Only Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Crown className="h-5 w-5 text-yellow-500" />
              Admin/Owner Only
            </CardTitle>
            <CardDescription>Content visible only to admins and owners</CardDescription>
          </CardHeader>
          <CardContent>
            <OrgRoleGuard
              requiredRoles={['admin', 'owner']}
              showRoleInfo
              fallback={
                <div className="text-center py-8 text-gray-500">
                  <Crown className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>Admin or Owner access required</p>
                </div>
              }
            >
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-green-600">
                  <UserCheck className="h-4 w-4" />
                  <span className="font-medium">Admin Dashboard Access</span>
                </div>
                <Button className="w-full" variant="default">
                  <Settings className="h-4 w-4 mr-2" />
                  Organization Settings
                </Button>
                <Button className="w-full" variant="outline">
                  <Users className="h-4 w-4 mr-2" />
                  Manage Team Members
                </Button>
              </div>
            </OrgRoleGuard>
          </CardContent>
        </Card>

        {/* Member+ Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-500" />
              Member+ Access
            </CardTitle>
            <CardDescription>Content for members, admins, and owners</CardDescription>
          </CardHeader>
          <CardContent>
            <OrgRoleGuard
              requiredRoles={['member', 'admin', 'owner']}
              showRoleInfo
              fallback={
                <div className="text-center py-8 text-gray-500">
                  <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>Member access required</p>
                </div>
              }
            >
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-blue-600">
                  <UserCheck className="h-4 w-4" />
                  <span className="font-medium">Team Member Features</span>
                </div>
                <Button className="w-full" variant="default">
                  View Team Resources
                </Button>
                <Button className="w-full" variant="outline">
                  Submit Reports
                </Button>
              </div>
            </OrgRoleGuard>
          </CardContent>
        </Card>

        {/* Viewer+ Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5 text-gray-500" />
              Viewer+ Access
            </CardTitle>
            <CardDescription>Content for all organization members</CardDescription>
          </CardHeader>
          <CardContent>
            <OrgRoleGuard
              requiredRoles={['viewer', 'member', 'admin', 'owner']}
              showRoleInfo
              fallback={
                <div className="text-center py-8 text-gray-500">
                  <Eye className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>Organization access required</p>
                </div>
              }
            >
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-gray-600">
                  <UserCheck className="h-4 w-4" />
                  <span className="font-medium">Basic Organization Access</span>
                </div>
                <Button className="w-full" variant="default">
                  View Organization Info
                </Button>
                <Button className="w-full" variant="outline">
                  Read Documentation
                </Button>
              </div>
            </OrgRoleGuard>
          </CardContent>
        </Card>

        {/* Conditional Rendering Example */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-purple-500" />
              Conditional Rendering
            </CardTitle>
            <CardDescription>Using RoleCheck for conditional logic</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <RoleCheck roles={['admin', 'owner']}>
                {(hasAccess, userRole, org) => (
                  <div
                    className={`p-3 rounded-lg ${hasAccess ? 'bg-green-50 border border-green-200' : 'bg-gray-50 border border-gray-200'}`}
                  >
                    <div className="font-medium text-sm">
                      {hasAccess ? '✅ Admin Features Available' : '❌ Admin Features Locked'}
                    </div>
                    <div className="text-xs text-gray-600 mt-1">
                      Role: {userRole || 'none'} | Org: {org?.orgName || 'none'}
                    </div>
                  </div>
                )}
              </RoleCheck>

              <RoleCheck role="member">
                {(hasAccess) => (
                  <Button
                    className="w-full"
                    variant={hasAccess ? 'default' : 'outline'}
                    disabled={!hasAccess}
                  >
                    {hasAccess ? 'Access Member Portal' : 'Member Access Required'}
                  </Button>
                )}
              </RoleCheck>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Role Caching Demo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5 text-blue-500" />
            Enhanced Role Caching System
          </CardTitle>
          <CardDescription>
            New [emailId, orgId, role] caching structure for dynamic organization switching
          </CardDescription>
        </CardHeader>
        <CardContent>
          <EnhancedRoleCacheDemo />
        </CardContent>
      </Card>

      {/* Performance Info */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>🚀 Performance Benefits</CardTitle>
          <CardDescription>
            Advantages of the new organization-aware role checking system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-3 gap-4 text-sm">
            <div className="p-3 bg-blue-50 rounded-lg">
              <div className="font-medium text-blue-800">Granular Caching</div>
              <div className="text-blue-600 mt-1">
                Roles cached per [email, org] combination for instant switching
              </div>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <div className="font-medium text-green-800">Dynamic Fetching</div>
              <div className="text-green-600 mt-1">
                Automatic role fetching when switching organizations
              </div>
            </div>
            <div className="p-3 bg-purple-50 rounded-lg">
              <div className="font-medium text-purple-800">Real-time Updates</div>
              <div className="text-purple-600 mt-1">
                Cache updates when roles change without full refresh
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Demo component for enhanced role caching system
 */
function EnhancedRoleCacheDemo() {
  const [testEmail, setTestEmail] = React.useState('<EMAIL>');
  const [testOrgId, setTestOrgId] = React.useState('');
  const { role, organization, loading } = useOrgRole();

  // Get organizations for testing
  const { getOrganizations, getCurrentOrgContext } = usePermissions();
  const organizations = getOrganizations();
  const currentOrgId = getCurrentOrgContext();

  // Use enhanced role hook for demonstration
  const enhancedRole = useEnhancedOrgRole(testEmail, testOrgId || currentOrgId || '');

  React.useEffect(() => {
    if (organizations.length > 0 && !testOrgId) {
      setTestOrgId(organizations[0].orgId);
    }
  }, [organizations, testOrgId]);

  return (
    <div className="space-y-6">
      {/* Current Implementation */}
      <div className="grid md:grid-cols-2 gap-4">
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h4 className="font-semibold text-yellow-800 mb-2">📚 Current System</h4>
          <div className="text-sm space-y-2">
            <div className="flex justify-between">
              <span>Your Role:</span>
              <Badge variant="outline">{role || 'none'}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Organization:</span>
              <Badge variant="outline">{organization?.orgName || 'none'}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Loading:</span>
              <Badge variant={loading ? 'destructive' : 'default'}>{loading ? 'Yes' : 'No'}</Badge>
            </div>
          </div>
        </div>

        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
          <h4 className="font-semibold text-green-800 mb-2 flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Enhanced System
          </h4>
          <div className="text-sm space-y-2">
            <div className="flex justify-between">
              <span>Cached Role:</span>
              <Badge variant="default">{enhancedRole.role || 'none'}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Can Manage:</span>
              <Badge variant={enhancedRole.canManage() ? 'default' : 'outline'}>
                {enhancedRole.canManage() ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Is Admin:</span>
              <Badge variant={enhancedRole.isAdmin() ? 'default' : 'outline'}>
                {enhancedRole.isAdmin() ? 'Yes' : 'No'}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Test Controls */}
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="font-semibold text-blue-800 mb-3">🧪 Test Enhanced Caching</h4>
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-blue-700 mb-1">Test Email:</label>
            <input
              type="email"
              value={testEmail}
              onChange={(e) => setTestEmail(e.target.value)}
              className="w-full px-3 py-2 border border-blue-300 rounded-md text-sm"
              placeholder="Enter email to test"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-blue-700 mb-1">
              Test Organization:
            </label>
            <select
              value={testOrgId}
              onChange={(e) => setTestOrgId(e.target.value)}
              className="w-full px-3 py-2 border border-blue-300 rounded-md text-sm"
            >
              {organizations.map((org) => (
                <option key={org.orgId} value={org.orgId}>
                  {org.orgName} ({org.role})
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="mt-4 p-3 bg-white rounded border">
          <div className="text-sm">
            <strong>Cache Key:</strong>{' '}
            <code className="bg-gray-100 px-2 py-1 rounded">
              {testEmail}:{testOrgId}
            </code>
          </div>
          <div className="text-sm mt-2">
            <strong>Query Status:</strong>
            <Badge variant={enhancedRole.isLoading ? 'destructive' : 'default'} className="ml-2">
              {enhancedRole.isLoading ? 'Loading' : 'Cached'}
            </Badge>
          </div>
        </div>
      </div>

      {/* Cache Management */}
      <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <h4 className="font-semibold text-gray-800 mb-3">⚙️ Cache Management</h4>
        <div className="grid md:grid-cols-3 gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => enhancedRole.switchToOrganization(testOrgId)}
            disabled={!testOrgId}
          >
            <Database className="h-4 w-4 mr-2" />
            Prefetch Role
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => enhancedRole.invalidateOrgRole(testEmail, testOrgId)}
            disabled={!testEmail || !testOrgId}
          >
            <Zap className="h-4 w-4 mr-2" />
            Invalidate Cache
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              enhancedRole.updateRoleInCache(testEmail, testOrgId, 'admin', 'Test Org')
            }
            disabled={!testEmail || !testOrgId}
          >
            <Settings className="h-4 w-4 mr-2" />
            Update Cache
          </Button>
        </div>

        <div className="mt-3 text-xs text-gray-600">
          <p>
            • <strong>Prefetch:</strong> Load role data for organization switching
          </p>
          <p>
            • <strong>Invalidate:</strong> Force refresh of cached role data
          </p>
          <p>
            • <strong>Update:</strong> Manually update role in cache (for real-time updates)
          </p>
        </div>
      </div>
    </div>
  );
}
