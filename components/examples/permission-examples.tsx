'use client';

import { usePermissions, PermissionGuard } from '@/hooks/use-permissions';
import { Permission } from '@/types/auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

/**
 * Example component showing different ways to use the RBAC system with QBraid organization roles
 */
export function PermissionExamples() {
  const {
    permissions,
    roles,
    orgRoles,
    hasPermission,
    hasAnyPermission,
    isAdmin,
    loading,
    refreshPermissions,
    // Multi-org functions
    getOrganizations,
    getUserRoleInOrg,
    getOrganizationById,
    getOrganizationsByRole,
    isAdminInAnyOrg,
    isAdminInOrg,
    getCurrentOrgContext,
  } = usePermissions();

  if (loading) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-4 bg-gray-200 rounded w-3/4" />
        <div className="h-4 bg-gray-200 rounded w-1/2" />
      </div>
    );
  }

  const organizations = getOrganizations();
  const currentOrgId = getCurrentOrgContext();

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">RBAC & Multi-Org Examples</h2>
        <Button onClick={refreshPermissions} className="mb-4">
          Refresh Permissions
        </Button>
      </div>

      {/* Current User Info */}
      <Card>
        <CardHeader>
          <CardTitle>Current User Info</CardTitle>
          <CardDescription>Your roles and permissions across all organizations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Overall Roles:</h4>
              <div className="flex flex-wrap gap-2">
                {roles.map((role) => (
                  <Badge key={role} variant="secondary">
                    {role}
                  </Badge>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">Organizations ({organizations.length}):</h4>
              <div className="space-y-2">
                {organizations.map((org) => (
                  <div
                    key={org.orgId}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded"
                  >
                    <div>
                      <span className="font-medium">{org.orgName}</span>
                      <span className="text-sm text-gray-500 ml-2">({org.orgId})</span>
                    </div>
                    <Badge variant={org.role === 'admin' ? 'default' : 'secondary'}>
                      {org.role}
                    </Badge>
                  </div>
                ))}
                {organizations.length === 0 && (
                  <div className="text-gray-500 italic">No organization data available</div>
                )}
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">Permissions ({permissions.length}):</h4>
              <div className="flex flex-wrap gap-1">
                {permissions.map((permission) => (
                  <Badge key={permission} variant="outline" className="text-xs">
                    {permission}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Multi-Org Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Multi-Organization Examples</CardTitle>
          <CardDescription>Examples of org-specific role checking</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Admin Status */}
            <div>
              <h4 className="font-medium mb-2">Admin Status:</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant={isAdmin() ? 'default' : 'secondary'}>
                    Global Admin: {isAdmin() ? 'Yes' : 'No'}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={isAdminInAnyOrg() ? 'default' : 'secondary'}>
                    Admin in Any Org: {isAdminInAnyOrg() ? 'Yes' : 'No'}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Org-Specific Examples */}
            {organizations.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Organization-Specific Examples:</h4>
                <div className="space-y-3">
                  {organizations.slice(0, 3).map((org) => (
                    <div key={org.orgId} className="p-3 border rounded">
                      <div className="font-medium mb-2">{org.orgName}</div>
                      <div className="space-y-1 text-sm">
                        <div>
                          Your role:{' '}
                          <Badge variant="secondary" className="ml-1">
                            {getUserRoleInOrg(org.orgId)}
                          </Badge>
                        </div>
                        <div>
                          Admin access:{' '}
                          <Badge
                            variant={isAdminInOrg(org.orgId) ? 'default' : 'secondary'}
                            className="ml-1"
                          >
                            {isAdminInOrg(org.orgId) ? 'Yes' : 'No'}
                          </Badge>
                        </div>
                        <div className="text-gray-500">
                          Last updated: {new Date(org.updated).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Current Context */}
            {currentOrgId && (
              <div>
                <h4 className="font-medium mb-2">Current Org Context:</h4>
                <Alert>
                  <AlertDescription>
                    Working in: <strong>{getOrganizationById(currentOrgId)?.orgName}</strong> (
                    {currentOrgId})
                  </AlertDescription>
                </Alert>
              </div>
            )}

            {/* Role-Based Filtering Example */}
            <div>
              <h4 className="font-medium mb-2">Role-Based Organization Filtering:</h4>
              <div className="space-y-2">
                {['admin', 'member', 'viewer'].map((role) => {
                  const orgsWithRole = getOrganizationsByRole(role);
                  return (
                    <div key={role} className="flex items-center gap-2">
                      <Badge variant="outline">{role}</Badge>
                      <span className="text-sm">
                        {orgsWithRole.length} organizations (
                        {orgsWithRole.map((o) => o.orgName).join(', ') || 'none'})
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Conditional Rendering Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Role-Based Access Examples</CardTitle>
          <CardDescription>
            Different access levels based on QBraid organization roles
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Simple permission check */}
          {hasPermission(Permission.ViewDevices) ? (
            <Alert>
              <AlertDescription>✅ You can view devices</AlertDescription>
            </Alert>
          ) : (
            <Alert variant="destructive">
              <AlertDescription>❌ You cannot view devices</AlertDescription>
            </Alert>
          )}

          {/* Multiple permission check */}
          {hasAnyPermission([Permission.ManageDevices, Permission.AdminAccess]) && (
            <Alert>
              <AlertDescription>✅ You can manage devices or have admin access</AlertDescription>
            </Alert>
          )}

          {/* Admin check */}
          {isAdmin() && (
            <Alert>
              <AlertDescription>✅ You are an organization administrator</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Permission Guard Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Permission Guard Examples</CardTitle>
          <CardDescription>Using PermissionGuard with QBraid organization roles</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Single permission guard */}
          <PermissionGuard
            permission={Permission.ViewDevices}
            fallback={
              <p className="text-gray-500">You need device viewing permission to see this.</p>
            }
          >
            <Button className="w-full">View All Devices</Button>
          </PermissionGuard>

          {/* Admin role guard */}
          <PermissionGuard
            role="admin"
            fallback={<p className="text-gray-500">Only organization admins can see this.</p>}
          >
            <Button variant="secondary" className="w-full">
              Organization Admin Panel
            </Button>
          </PermissionGuard>

          {/* Owner role guard */}
          <PermissionGuard
            role="owner"
            fallback={<p className="text-gray-500">Only organization owners can see this.</p>}
          >
            <Button variant="outline" className="w-full">
              Organization Owner Controls
            </Button>
          </PermissionGuard>

          {/* Multiple roles with OR logic */}
          <PermissionGuard
            roles={['admin', 'owner']}
            requireAll={false}
            fallback={<p className="text-gray-500">You need admin or owner role.</p>}
          >
            <Button variant="destructive" className="w-full">
              Delete Organization Resources
            </Button>
          </PermissionGuard>

          {/* Member access */}
          <PermissionGuard
            role="member"
            fallback={<p className="text-gray-500">Organization members only.</p>}
          >
            <Button variant="outline" className="w-full">
              Member Tools
            </Button>
          </PermissionGuard>
        </CardContent>
      </Card>

      {/* Real-world UI Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Organization Management Examples</CardTitle>
          <CardDescription>Practical examples for QBraid organization management</CardDescription>
        </CardHeader>
        <CardContent>
          <OrganizationManagementExample />
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Example of organization management with QBraid role-based permissions
 */
function OrganizationManagementExample() {
  // Note: This component uses PermissionGuard components for role checking

  const organizations = [
    { id: 1, name: 'Research Lab Alpha', devices: 3, members: 12, role: 'admin' },
    { id: 2, name: 'Quantum Computing Dept', devices: 8, members: 25, role: 'member' },
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Your Organizations</h3>

        {/* Only admins and owners can create organizations */}
        <PermissionGuard roles={['admin', 'owner']} requireAll={false}>
          <Button size="sm">Create Organization</Button>
        </PermissionGuard>
      </div>

      <div className="grid gap-4">
        {organizations.map((org) => (
          <div key={org.id} className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h4 className="font-medium">{org.name}</h4>
              <p className="text-sm text-gray-600">
                {org.devices} devices • {org.members} members • Your role: {org.role}
              </p>
            </div>

            <div className="flex gap-2">
              {/* All organization members can view */}
              <PermissionGuard roles={['admin', 'owner', 'member', 'viewer']}>
                <Button size="sm" variant="outline">
                  View Details
                </Button>
              </PermissionGuard>

              {/* Members and above can access tools */}
              <PermissionGuard roles={['admin', 'owner', 'member']}>
                <Button size="sm">Manage</Button>
              </PermissionGuard>

              {/* Only admins and owners can delete */}
              <PermissionGuard roles={['admin', 'owner']}>
                <Button size="sm" variant="destructive">
                  Delete
                </Button>
              </PermissionGuard>
            </div>
          </div>
        ))}
      </div>

      {/* Admin-only organization controls */}
      <PermissionGuard
        permission={Permission.AdminAccess}
        fallback={
          <div className="p-4 bg-gray-50 rounded-lg text-center text-gray-500">
            Organization admin controls are hidden
          </div>
        }
      >
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-800 mb-2">Organization Admin Controls</h4>
          <div className="flex gap-2">
            <Button size="sm" variant="outline">
              Manage Members
            </Button>
            <Button size="sm" variant="outline">
              Billing Settings
            </Button>
            <Button size="sm" variant="outline">
              Organization Settings
            </Button>
          </div>
        </div>
      </PermissionGuard>

      {/* Owner-only controls */}
      <PermissionGuard role="owner" fallback={null}>
        <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
          <h4 className="font-medium text-purple-800 mb-2">Organization Owner Controls</h4>
          <div className="flex gap-2">
            <Button size="sm" variant="outline">
              Transfer Ownership
            </Button>
            <Button size="sm" variant="outline">
              Delete Organization
            </Button>
            <Button size="sm" variant="outline">
              Advanced Settings
            </Button>
          </div>
        </div>
      </PermissionGuard>
    </div>
  );
}
