'use client';

import React, { createContext, useContext, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { usePermissions, permissionKeys, useInvalidatePermissions } from '@/hooks/use-permissions';
import { Permission } from '@/types/auth';

interface PermissionContextType {
  permissions: Permission[];
  roles: string[];
  hasPermission: (permission: Permission) => boolean;
  hasAnyPermission: (permissions: Permission[]) => boolean;
  hasRole: (role: string) => boolean;
  isAdmin: () => boolean;
  loading: boolean;
  refreshPermissions: () => void;
  refreshRolesFromAPI: () => void;
}

const PermissionContext = createContext<PermissionContextType | null>(null);

/**
 * Global permission provider that integrates with TanStack Query
 * Provides permission state throughout the application
 */
export function PermissionProvider({ children }: { children: React.ReactNode }) {
  const queryClient = useQueryClient();
  const { invalidatePermissions } = useInvalidatePermissions();
  const permissionData = usePermissions();

  // Set up global event listeners for permission updates
  useEffect(() => {
    // Listen for storage events (multi-tab synchronization)
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'permissions-updated') {
        invalidatePermissions();
      }
    };

    // Listen for custom permission update events
    const handlePermissionUpdate = () => {
      invalidatePermissions();
    };

    // Listen for visibility change to refresh stale data
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Invalidate if data is stale (older than 5 minutes)
        const permissionsData = queryClient.getQueryData(permissionKeys.current());
        if (permissionsData && permissionData.isStale) {
          invalidatePermissions();
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('permission-updated', handlePermissionUpdate);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('permission-updated', handlePermissionUpdate);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [invalidatePermissions, queryClient, permissionData.isStale]);

  // Broadcast permission updates to other tabs
  const broadcastPermissionUpdate = () => {
    localStorage.setItem('permissions-updated', Date.now().toString());
    localStorage.removeItem('permissions-updated'); // Clean up immediately

    // Dispatch custom event for same-tab components
    window.dispatchEvent(new CustomEvent('permission-updated'));
  };

  // Enhanced refresh functions with broadcasting
  const refreshPermissions = () => {
    permissionData.refreshPermissions();
    broadcastPermissionUpdate();
  };

  const refreshRolesFromAPI = () => {
    permissionData.refreshRolesFromAPI();
    broadcastPermissionUpdate();
  };

  const contextValue: PermissionContextType = {
    permissions: permissionData.permissions,
    roles: permissionData.roles,
    hasPermission: permissionData.hasPermission,
    hasAnyPermission: permissionData.hasAnyPermission,
    hasRole: permissionData.hasRole,
    isAdmin: permissionData.isAdmin,
    loading: permissionData.loading,
    refreshPermissions,
    refreshRolesFromAPI,
  };

  return <PermissionContext.Provider value={contextValue}>{children}</PermissionContext.Provider>;
}

/**
 * Hook to access permission context
 * Provides global permission state with TanStack Query optimization
 */
export function usePermissionContext(): PermissionContextType {
  const context = useContext(PermissionContext);

  if (!context) {
    throw new Error('usePermissionContext must be used within a PermissionProvider');
  }

  return context;
}

/**
 * HOC for components that need permission context
 */
export function withPermissions<P extends object>(Component: React.ComponentType<P>) {
  return function PermissionWrappedComponent(props: P) {
    return (
      <PermissionProvider>
        <Component {...props} />
      </PermissionProvider>
    );
  };
}
