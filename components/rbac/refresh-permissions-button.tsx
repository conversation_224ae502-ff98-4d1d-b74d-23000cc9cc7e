'use client';

import React from 'react';
import { RefreshCw, Download, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { usePermissions } from '@/hooks/use-permissions';

interface RefreshPermissionsButtonProps {
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  showStatus?: boolean;
  showRoleCount?: boolean;
  className?: string;
}

/**
 * Smart refresh button component with TanStack Query integration
 * Shows loading states, success/error feedback, and role information
 */
export function RefreshPermissionsButton({
  variant = 'outline',
  size = 'sm',
  showStatus = true,
  showRoleCount = true,
  className = '',
}: RefreshPermissionsButtonProps) {
  const { toast } = useToast();
  const {
    permissions,
    roles,
    loading,
    isRefreshing,
    error,
    refreshError,
    refreshPermissions,
    refreshRolesFromAPI,
    isStale,
  } = usePermissions();

  const handleRefreshPermissions = () => {
    refreshPermissions();
    toast({
      title: 'Refreshing Permissions',
      description: 'Updating your current permissions from session...',
    });
  };

  const handleRefreshRoles = () => {
    refreshRolesFromAPI();
    toast({
      title: 'Refreshing Roles',
      description: 'Fetching latest roles from QBraid API...',
    });
  };

  const getStatusIcon = () => {
    if (loading || isRefreshing) {
      return <RefreshCw className="h-4 w-4 animate-spin" />;
    }
    if (error || refreshError) {
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    }
    if (isStale) {
      return <Download className="h-4 w-4 text-orange-500" />;
    }
    return <CheckCircle className="h-4 w-4 text-green-500" />;
  };

  const getStatusText = () => {
    if (loading) return 'Loading...';
    if (isRefreshing) return 'Refreshing...';
    if (error || refreshError) return 'Error';
    if (isStale) return 'Stale Data';
    return 'Up to date';
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Quick permission refresh */}
      <Button
        variant={variant}
        size={size}
        onClick={handleRefreshPermissions}
        disabled={loading || isRefreshing}
        className="flex items-center gap-2"
      >
        <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
        Refresh Permissions
      </Button>

      {/* Full role refresh from QBraid API */}
      <Button
        variant="ghost"
        size={size}
        onClick={handleRefreshRoles}
        disabled={loading || isRefreshing}
        className="flex items-center gap-2"
        title="Refresh roles from QBraid API"
      >
        <Download className={`h-4 w-4 ${isRefreshing ? 'animate-bounce' : ''}`} />
        Refresh Roles
      </Button>

      {/* Status indicator */}
      {showStatus && (
        <div className="flex items-center gap-1">
          {getStatusIcon()}
          <span className="text-xs text-muted-foreground">{getStatusText()}</span>
        </div>
      )}

      {/* Role count badge */}
      {showRoleCount && roles.length > 0 && (
        <Badge variant="secondary" className="text-xs">
          {roles.length} role{roles.length !== 1 ? 's' : ''}
        </Badge>
      )}

      {/* Permission count badge */}
      {showRoleCount && permissions.length > 0 && (
        <Badge variant="outline" className="text-xs">
          {permissions.length} permission{permissions.length !== 1 ? 's' : ''}
        </Badge>
      )}
    </div>
  );
}

/**
 * Minimal refresh icon button for tight spaces
 */
export function RefreshPermissionsIcon({
  className = '',
  ...props
}: RefreshPermissionsButtonProps) {
  const { refreshPermissions, loading, isRefreshing } = usePermissions();

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={refreshPermissions}
      disabled={loading || isRefreshing}
      className={`h-8 w-8 p-0 ${className}`}
      title="Refresh permissions"
      {...props}
    >
      <RefreshCw className={`h-4 w-4 ${loading || isRefreshing ? 'animate-spin' : ''}`} />
    </Button>
  );
}
