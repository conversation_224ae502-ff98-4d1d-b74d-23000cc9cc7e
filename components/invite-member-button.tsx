'use client';

import { useState, useId } from 'react';
import <PERSON> from 'papaparse';
import { Users, Plus, Trash2 } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';

import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface InvitedUser {
  id: string;
  email: string;
  role: string;
}

interface InviteUsersProps {
  roles: { name: string }[];
  onInviteUser?: (email: string, role: string) => void;
  onClear?: () => void;
  onCancel?: () => void;
}

export function InviteUsers({ roles, onInviteUser, onClear, onCancel }: InviteUsersProps) {
  const [invitedUsers, setInvitedUsers] = useState<InvitedUser[]>([]);
  const [currentEmail, setCurrentEmail] = useState('');
  const [currentRole, setCurrentRole] = useState('user');
  const [emailError, setEmailError] = useState('');
  const csvUploadId = useId();
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentEmail.trim()) {
      setEmailError('Email is required');
      return;
    }

    if (!emailRegex.test(currentEmail)) {
      setEmailError('Invalid email format');
      return;
    }

    if (invitedUsers.some((user) => user.email.toLowerCase() === currentEmail.toLowerCase())) {
      setEmailError('User already added');
      return;
    }

    setInvitedUsers([
      {
        id: uuidv4(),
        email: currentEmail,
        role: currentRole,
      },
      ...invitedUsers,
    ]);
    setCurrentEmail('');
    setEmailError('');
  };

  const removeUser = (id: string) => {
    setInvitedUsers((prev) => prev.filter((u) => u.id !== id));
  };

  const updateUserRole = (id: string, newRole: string) => {
    setInvitedUsers((prev) => prev.map((u) => (u.id === id ? { ...u, role: newRole } : u)));
  };

  const handleClear = () => {
    setInvitedUsers([]);
    setCurrentEmail('');
    setEmailError('');
    onClear?.();
  };

  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleInviteAll = async () => {
    if (!onInviteUser) return;

    try {
      for (const u of invitedUsers) {
        await onInviteUser(u.email, u.role);
      }
      setInvitedUsers([]);
      setStatus('success');
      onCancel?.();
    } catch (error) {
      console.error('Failed to invite users:', error);
      setStatus('error');
    }
  };

  const [csvErrors, setCsvErrors] = useState<string[]>([]);

  const handleCsvFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      const csvOutput = event.target?.result as string;
      const { data } = Papa.parse(csvOutput, { header: true, skipEmptyLines: true });

      const newUsers: InvitedUser[] = [];
      const errors: string[] = [];

      (data as any[]).forEach((row, index) => {
        const rowNumber = index + 2; // +2 because CSV header is row 1
        const email = row.email?.trim();
        const role = row.role?.toLowerCase();

        if (!email || !emailRegex.test(email)) {
          errors.push(`Row ${rowNumber}: Invalid email "${email}"`);
          return;
        }

        if (!role || !roles.map((r) => r.name.toLowerCase()).includes(role)) {
          errors.push(`Row ${rowNumber}: Invalid role "${role}"`);
          return;
        }

        if (role === 'owner') {
          errors.push(`Row ${rowNumber}: Role "owner" is not allowed`);
          return;
        }

        if (
          invitedUsers.some((u) => u.email === email) ||
          newUsers.some((u) => u.email === email)
        ) {
          errors.push(`Row ${rowNumber}: Duplicate email "${email}"`);
          return;
        }

        newUsers.push({ id: uuidv4(), email, role });
      });

      if (newUsers.length > 0) {
        setInvitedUsers((prev) => [...newUsers, ...prev]);
      }

      setCsvErrors(errors);
    };

    reader.onloadend = () => {
      e.target.value = '';
    };

    reader.readAsText(file);
  };

  return (
    <Dialog open onOpenChange={onCancel}>
      <DialogContent className="sm:max-w-4xl bg-[#121212] text-white border-gray-800">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Invite Users
          </DialogTitle>
          <p className="text-sm text-gray-400">Add users by email and assign their roles.</p>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Existing invited users */}
          {invitedUsers.length > 0 && (
            <div
              className={`mb-4 ${invitedUsers.length > 3 ? 'max-h-40 overflow-y-auto' : ''} always-show-scrollbar`}
            >
              {invitedUsers.map((user) => (
                <div key={user.id} className="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-400">Email</Label>
                    <Input
                      value={user.email}
                      readOnly
                      className="bg-[#1a1a1a] border-gray-800 text-white"
                    />
                  </div>
                  <div className="flex gap-2 items-end">
                    <div className="flex-1 space-y-2">
                      <Label className="text-sm text-gray-400">Role</Label>
                      <Select
                        value={user.role}
                        onValueChange={(value) => updateUserRole(user.id, value)}
                      >
                        <SelectTrigger className="bg-[#1a1a1a] border-gray-800 text-white">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-[#1a1a1a] border-gray-800 text-white">
                          {roles
                            .filter((r) => r.name.toLowerCase() !== 'owner')
                            .map((r) => (
                              <SelectItem key={r.name} value={r.name.toLowerCase()}>
                                {r.name}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <Button
                      type="button"
                      size="icon"
                      variant="destructive"
                      className="bg-red-600 hover:bg-red-700"
                      onClick={() => removeUser(user.id)}
                      aria-label="Trash icon to remove user before adding to company"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* New invite form */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
            <div className="space-y-2">
              <Label className="text-sm text-gray-400">Email</Label>
              <Input
                type="email"
                value={currentEmail}
                onChange={(e) => {
                  setCurrentEmail(e.target.value);
                  if (emailError) setEmailError('');
                }}
                className={`bg-[#1a1a1a] border-gray-800 text-white ${emailError ? 'border-red-500' : ''}`}
                placeholder="Enter user's email"
              />
              {emailError && <p className="text-sm text-red-400">{emailError}</p>}
            </div>

            <div className="flex gap-2 items-end">
              <div className="flex-1 space-y-2">
                <Label className="text-sm text-gray-400">Role</Label>
                <Select value={currentRole} onValueChange={setCurrentRole}>
                  <SelectTrigger className="bg-[#1a1a1a] border-gray-800 text-white">
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent className="bg-[#1a1a1a] border-gray-800 text-white">
                    {roles
                      .filter((r) => r.name.toLowerCase() !== 'owner')
                      .map((r) => (
                        <SelectItem key={r.name} value={r.name.toLowerCase()}>
                          {r.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
              <Button
                type="submit"
                size="icon"
                className="bg-purple-600 hover:bg-purple-700"
                aria-label="Add user"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </form>

        {/* CSV Upload */}
        <div className="pt-4">
          <input type="file" accept=".csv" hidden id={csvUploadId} onChange={handleCsvFileChange} />
          <Button
            type="button"
            onClick={() => document.getElementById(csvUploadId)?.click()}
            className="text-sm text-purple-400 hover:underline"
            aria-label="Add user from CSV"
          >
            + Add users from CSV
          </Button>

          {csvErrors.length > 0 && (
            <div className="bg-red-900/20 border border-red-700 text-red-400 text-sm rounded p-3 max-h-40 overflow-y-auto">
              <p className="font-semibold mb-1">Some rows had issues:</p>
              <ul className="list-disc list-inside space-y-1">
                {csvErrors.map((err, i) => (
                  <li key={`csv-error-${i}`}>{err}</li>
                ))}
              </ul>
            </div>
          )}
        </div>

        <DialogFooter className="pt-6 border-t border-gray-800 justify-end">
          <Button
            variant="outline"
            onClick={handleClear}
            className="bg-[#1a1a1a] border-gray-700 text-white hover:bg-gray-800"
            aria-label="clear ad user pop up"
          >
            Clear
          </Button>
          <Button
            onClick={handleInviteAll}
            disabled={invitedUsers.length === 0}
            className="bg-purple-700 hover:bg-purple-600"
            aria-label="Invite users button"
          >
            Invite User{invitedUsers.length !== 1 ? 's' : ''}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
