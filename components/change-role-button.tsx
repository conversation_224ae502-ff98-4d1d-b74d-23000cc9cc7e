'use client';

import { useState, useRef, useEffect } from 'react';
import { Check, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { TeamMember } from '@/types/team';

interface Role {
  name: string;
  permissions: string[];
}

interface ChangeRoleButtonProps {
  user: TeamMember;
  onRoleChange: (user: TeamMember, role: string) => Promise<void>;
  onClose: () => void;
  roles: Role[];
}

export function ChangeRoleButton({ user, onRoleChange, onClose, roles }: ChangeRoleButtonProps) {
  const [selectedRole, setSelectedRole] = useState(user.role);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const email = user.email;

  ////////////////////////////////
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [dropdownMaxHeight, setDropdownMaxHeight] = useState('18rem'); // fallback

  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    if (dropdownOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom - 16; // 16px padding
      setDropdownMaxHeight(`${spaceBelow}px`);
    }
  }, [dropdownOpen]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
    }

    if (dropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dropdownOpen]);

  const handleSave = () => {
    setConfirmOpen(true);
  };

  const handleConfirm = async () => {
    try {
      await onRoleChange(user, selectedRole);
      setConfirmOpen(false);
      onClose();
    } catch (err: any) {
      setErrorMessage(err.message || 'Failed to update role. Please try again.');
      setConfirmOpen(false);
    }
  };

  return (
    <>
      <Dialog open onOpenChange={onClose}>
        <DialogContent className="sm:max-w-xl bg-[#121212] text-white border-gray-800">
          <DialogHeader>
            <DialogTitle>Change Role</DialogTitle>
            <p className="text-sm text-gray-400">Select a new role and save changes.</p>
          </DialogHeader>

          <div className="space-y-6 py-4">
            <div className="space-y-2">
              <label className="text-sm text-gray-400">Email</label>
              <div className="text-white">{email}</div>
            </div>

            <div className="space-y-2">
              <label className="text-sm text-gray-400">Role</label>
              <div className="relative" ref={dropdownRef}>
                <button
                  ref={buttonRef}
                  onClick={() => setDropdownOpen(!dropdownOpen)}
                  className="flex items-center justify-between w-full p-3 bg-[#1a1a1a] border border-gray-800 rounded-md"
                >
                  <span className="capitalize text-purple-400">{selectedRole}</span>
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                </button>

                {dropdownOpen && (
                  <div
                    style={{ maxHeight: dropdownMaxHeight }}
                    className={`
                      absolute z-10 mt-1 w-full bg-[#1a1a1a] border border-gray-800 rounded-md shadow-lg overflow-y-auto
                      transition-all duration-200 ease-out origin-top animate-dropdown
                    `}
                  >
                    {roles
                      .filter((role) => role.name !== 'Owner')
                      .map((role) => (
                        <div
                          key={role.name}
                          className={`p-4 cursor-pointer hover:bg-purple-700 ${
                            role.name === selectedRole ? 'bg-purple-700' : ''
                          }`}
                          onClick={() => {
                            setSelectedRole(role.name);
                            setDropdownOpen(false);
                          }}
                        >
                          <div className="font-medium">{role.name}</div>
                          <div className="mt-2 space-y-1">
                            {role.permissions.map((permission, index) => (
                              <div key={index} className="flex items-start text-sm text-gray-300">
                                <span className="mr-2">•</span>
                                <span>{permission}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {errorMessage && (
            <div className="bg-red-900/20 border border-red-700 text-red-400 text-sm rounded p-3 mb-4">
              {errorMessage}
            </div>
          )}

          <DialogFooter className="justify-end">
            <Button
              onClick={handleSave}
              className="bg-purple-700 hover:bg-purple-600"
              disabled={selectedRole === user.role}
              aria-label="Save role change"
            >
              <Check className="mr-2 h-4 w-4" />
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <AlertDialog open={confirmOpen} onOpenChange={setConfirmOpen}>
        <AlertDialogContent className="bg-[#121212] text-white border-gray-800 max-w-xl">
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Role Change</AlertDialogTitle>
            <AlertDialogDescription className="text-gray-300 text-center">
              <span>
                Are you sure you want to make <span className="font-bold text-white">{email}</span>{' '}
                into <span className="font-bold text-white">{selectedRole}</span>?
              </span>
              <br />
              <br />
              <span className="text-red-500 font-bold">
                This action will update their permissions immediately.
              </span>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              className="bg-transparent border-gray-700 hover:bg-gray-800 hover:text-white"
              aria-label="cancel role change"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirm}
              className="bg-purple-700 hover:bg-purple-600"
              aria-label="confirm role change"
            >
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
