/**
 * JobsRow component
 * -----------------
 * This module exports the JobsRow React component and its props interface.
 * It is used to render a single row in the jobs table on the device-jobs-tab,
 * displaying information about a quantum job such as job ID, device ID, timestamps,
 * queue position, status, vendor, provider, shots, cost, and experiment type.
 * The component is designed to be used within a table and expects props matching JobsRowProps.
 */

import type { JobsRowProps } from '@/types/jobs';

function TruncateField(fullField?: string) {
  let field: string;
  // If fullField is undefined, null, or an empty string, return 'N/A'
  if (!fullField || fullField == null || fullField == '') {
    return 'N/A';
  }
  // If the fullField is longer than 20 characters, truncate it and add ellipsis
  if (fullField.length > 20) {
    field = fullField.slice(0, 20) + '...';
  } else {
    field = fullField;
  }
  return field;
}

function JobRowElement({ field, title }: { field?: string; title?: string }) {
  if (!field || field == null || field == '') {
    field = 'N/A';
  }
  return (
    <td className="py-4 px-4">
      <div className="flex items-center space-x-3">
        <p className="text-white font-medium text-xs" title={title || undefined}>
          {field}
        </p>
      </div>
    </td>
  );
}

function formatNumber(val?: number, digits: number = 0) {
  return val !== undefined && val !== null ? val.toFixed(digits) : 'N/A';
}

function JobRowElementDate({ createdAt, endedAt }: { createdAt?: Date; endedAt?: Date }) {
  if (createdAt && endedAt) {
    return (
      <td className="py-4">
        <div className="flex items-center justify-between px-4">
          <p className="text-[#94a3b8] text-xs">
            {createdAt.toLocaleString()} /<br></br>
            {endedAt.toLocaleString()}
          </p>
        </div>
      </td>
    );
  } else {
    return (
      <td className="py-4">
        <div className="flex items-center justify-between px-4">
          <p className="text-[#94a3b8] text-xs">N/A</p>
        </div>
      </td>
    );
  }
}

export function JobsTableRow({
  qbraidDeviceId,
  timeStamps,
  queuePosition,
  qbraidStatus,
  vendor,
  provider,
  shots,
  cost,
  experimentType,
}: JobsRowProps) {
  return (
    <tr className="border-b border-[#3b3b3b] hover:bg-[#262131]/50">
      <JobRowElement title={qbraidDeviceId} field={TruncateField(qbraidDeviceId)}></JobRowElement>
      <JobRowElementDate
        createdAt={timeStamps?.createdAt ? new Date(timeStamps.createdAt) : undefined}
        endedAt={timeStamps?.endedAt ? new Date(timeStamps.endedAt) : undefined}
      ></JobRowElementDate>
      <JobRowElement field={formatNumber(queuePosition, 0)}></JobRowElement>
      <JobRowElement title={qbraidStatus} field={TruncateField(qbraidStatus)}></JobRowElement>
      <JobRowElement title={vendor} field={TruncateField(vendor)}></JobRowElement>
      <JobRowElement title={provider} field={TruncateField(provider)}></JobRowElement>
      <JobRowElement field={formatNumber(shots, 0)}></JobRowElement>
      <JobRowElement field={formatNumber(cost, 2)}></JobRowElement>
      <JobRowElement title={experimentType} field={TruncateField(experimentType)}></JobRowElement>
    </tr>
  );
}
