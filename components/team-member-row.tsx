'use client';

import { MoreHorizontal } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface TeamMemberRowProps {
  user: {
    name: string;
    email: string;
    avatar?: string;
    role: string;
    status: 'Active' | 'Deactivated' | 'Invited';
    userCredits: number;
    lastActive: string;
  };
  onChangeRole?: () => void;
  onRemove?: () => void;
}

export function TeamMemberRow({ user, onChangeRole, onRemove }: TeamMemberRowProps) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Active':
        return <Badge className="bg-[#22c55e] text-white hover:bg-[#22c55e]/90">Active</Badge>;
      case 'Deactivated':
        return <Badge className="bg-[#ef4444] text-white hover:bg-[#ef4444]/90">Deactivated</Badge>;
      case 'Invited':
        return <Badge className="bg-[#3b82f6] text-white hover:bg-[#3b82f6]/90">Invited</Badge>;
      default:
        return <Badge className="bg-[#94a3b8] text-white">Unknown</Badge>;
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  return (
    <tr className="border-b border-[#3b3b3b] hover:bg-[#262131]/50">
      <td className="py-4 px-4">
        <div className="flex items-center space-x-3">
          <Avatar className="w-8 h-8">
            {/* <AvatarImage src={user.avatar} /> */}
            <AvatarImage src={user.avatar || '/placeholder.svg'} />
            <AvatarFallback className="bg-[#8a2be2] text-white text-xs">
              {getInitials(user.name)}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="text-white font-medium text-sm">{user.name}</p>
            <p className="text-[#94a3b8] text-xs">{user.email}</p>
          </div>
        </div>
      </td>
      <td className="py-4 px-4">
        <span className="text-[#94a3b8] text-sm">{user.role}</span>
      </td>
      <td className="py-4 px-4">
        <span className="text-[#94a3b8] text-sm">{user.userCredits}</span>
      </td>
      <td className="py-4 px-4">{getStatusBadge(user.status)}</td>
      <td className="py-4 px-4">
        <span className="text-[#94a3b8] text-sm">{user.lastActive}</span>
      </td>
      <td className="py-4 px-4">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="text-[#94a3b8] hover:text-white p-1">
              <MoreHorizontal className="w-4 h-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="bg-[#262131] border-[#3b3b3b]">
            {/* TODO - need to implement a way to allow only owner, superadmin, and admin to be able to invite members and remove */}
            {/* TODO - have managers be able to access only users to remove member */}
            {/* TODO - Users should not have any acess to change role or remove member */}
            <DropdownMenuItem
              className="text-[#94a3b8] hover:text-white hover:bg-[#3b3b3b]"
              onClick={onChangeRole}
            >
              Change Role
            </DropdownMenuItem>

            <DropdownMenuItem
              className="text-[#ef4444] hover:text-[#ef4444] hover:bg-[#3b3b3b]"
              onClick={onRemove}
            >
              Remove Member
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </td>
    </tr>
  );
}
