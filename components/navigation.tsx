import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { LogoutButton } from '@/components/auth/logout-button';
import { useState, useEffect, useRef, useLayoutEffect } from 'react';

interface NavigationProps {
  activeTab: string;
}

export function Navigation({ activeTab }: NavigationProps) {
  const [scrolled, setScrolled] = useState(false);
  useEffect(() => {
    const handleScroll = () => setScrolled(window.scrollY > 10);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const tabs = [
    { name: 'Devices', href: '/' },
    { name: 'Earnings', href: '/earnings' },
    { name: 'Team', href: '/team' },
    { name: 'RBAC Demo', href: '/rbac-demo' },
    {
      name: 'Documentation',
      href: 'https://docs.qbraid.com/',
      target: '_blank',
      rel: 'noopener noreferrer',
    },
  ];

  // refs and state for animated bubble
  const containerRef = useRef<HTMLDivElement>(null);
  const tabRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [bubbleStyle, setBubbleStyle] = useState({ left: 0, width: 0 });

  const updateBubble = () => {
    const idx = tabs.findIndex((tab) => tab.name.toLowerCase() === activeTab);
    const ref = tabRefs.current[idx];
    if (ref) {
      setBubbleStyle({ left: ref.offsetLeft, width: ref.offsetWidth });
    }
  };

  useLayoutEffect(() => {
    updateBubble();
    window.addEventListener('resize', updateBubble);
    return () => window.removeEventListener('resize', updateBubble);
  }, [activeTab]);

  // Animated nav with bubble under active tab
  return (
    <nav
      className={`fixed top-0 left-0 w-full h-16 backdrop-blur-lg transition-colors duration-300 ${scrolled ? 'bg-[#18141f]/30' : 'bg-transparent'} z-[9999]`}
    >
      <div className="flex items-center justify-between px-6 h-full">
        {/* Left: logo */}
        <Link href="/" className="flex items-center space-x-2">
          <img src="/favicon_32.png" alt="qBraid logo" className="w-8 h-8" />
          <span className="text-white font-medium text-lg">qBraid</span>
        </Link>

        {/* Center: tabs within a pill container with sliding highlight */}
        <div className="flex-1 flex justify-center items-center h-full">
          <div
            ref={containerRef}
            className="relative flex items-center bg-[#262131]/50 rounded-full py-2 gap-6"
          >
            {/* Sliding highlight */}
            <div
              className="absolute py-5 bg-[#8a2be2]/20 rounded-full transition-all duration-500 ease-out"
              style={{ left: bubbleStyle.left, width: bubbleStyle.width }}
            />
            {tabs.map((tab, idx) => (
              <div
                key={tab.name}
                ref={(el) => {
                  tabRefs.current[idx] = el;
                }}
                className="relative z-10"
              >
                <Link
                  href={tab.href}
                  target={tab.target || '_self'}
                  rel={tab.rel || undefined}
                  className={`px-4 py-2.5 text-md font-normal transition-colors ${
                    activeTab === tab.name.toLowerCase()
                      ? 'text-white'
                      : 'text-[#94a3b8] hover:text-white'
                  } ${tab.name === 'Documentation' ? 'bg-[#252b36] rounded-full' : ''}`}
                >
                  {tab.name}
                </Link>
              </div>
            ))}
          </div>
        </div>

        {/* Right: Log Out button with auth functionality */}
        <LogoutButton
          variant="outline"
          className="bg-[#8a2be2] border-[#8a2be2] text-white hover:bg-[#8a2be2]/90 hover:border-[#8a2be2]/90"
        >
          Log Out
        </LogoutButton>
      </div>
    </nav>
  );
}
