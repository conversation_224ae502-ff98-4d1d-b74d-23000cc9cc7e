import { <PERSON><PERSON><PERSON>, Linkedin, <PERSON> as Twitter } from 'lucide-react';

export default function Footer() {
  return (
    <footer className="fixed bottom-0 w-full bg-[#18141f] border-t border-gray-800 py-2 z-50">
      <div className="container mx-auto px-4 flex flex-col md:flex-row md:items-center justify-between text-sm text-gray-400 space-y-4 md:space-y-0">
        {/* Left: Logo + Copyright */}
        <div className="flex items-center space-x-2">
          <img src="/qbraid_logo.png" alt="qBraid Logo" className="h-6 w-auto" />
          <span>&copy; 2025 qBraid Co.</span>
        </div>

        {/* Center: Links */}
        <div className="flex flex-wrap justify-center space-x-4">
          <a href="/" className="hover:text-white transition">
            Home
          </a>
          <a
            href="https://docs.qbraid.com/home/<USER>"
            target="_blank"
            className="hover:text-white transition"
          >
            Docs
          </a>
          <a
            href="https://www.qbraid.com/careers"
            target="_blank"
            className="hover:text-white transition"
          >
            Careers
          </a>
          <a
            href="https://account.qbraid.com/terms-and-conditions"
            target="_blank"
            className="hover:text-white transition"
          >
            Terms & Conditions
          </a>
          <a
            href="https://account.qbraid.com/cookie-policy"
            target="_blank"
            className="hover:text-white transition"
          >
            Cookie Policy
          </a>
          <a
            href="https://account.qbraid.com/privacy-policy"
            target="_blank"
            className="hover:text-white transition"
          >
            Privacy Policy
          </a>
        </div>

        {/* Right: Social Icons */}
        <div className="flex items-center space-x-4">
          <a
            href="https://x.com/qbraid_official"
            target="_blank"
            rel="noopener noreferrer"
            className="hover:text-white transition"
          >
            <Twitter className="h-4 w-4" />
          </a>
          <a
            href="https://discord.com/invite/9jpmpeEV65"
            target="_blank"
            rel="noopener noreferrer"
            className="hover:text-white transition"
            aria-label="Discord"
          >
            <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
              <path d="M20.317 4.369A19.791 19.791 0 0 0 16.885 3.2a.074.074 0 0 0-.078.037c-.34.607-.719 1.396-.984 2.013a18.524 18.524 0 0 0-5.59 0 12.51 12.51 0 0 0-.997-2.013.077.077 0 0 0-.078-.037A19.736 19.736 0 0 0 3.677 4.369a.069.069 0 0 0-.032.027C.533 9.09-.32 13.579.099 18.021a.082.082 0 0 0 .031.056c2.104 1.548 4.13 2.489 6.102 3.115a.077.077 0 0 0 .084-.027c.472-.65.893-1.34 1.248-2.065a.076.076 0 0 0-.041-.104c-.662-.25-1.293-.549-1.902-.892a.077.077 0 0 1-.008-.127c.128-.096.256-.197.378-.299a.074.074 0 0 1 .077-.01c3.993 1.826 8.285 1.826 12.251 0a.073.073 0 0 1 .078.009c.122.102.25.203.379.299a.077.077 0 0 1-.006.127 12.298 12.298 0 0 1-1.904.892.076.076 0 0 0-.04.105c.36.724.78 1.414 1.247 2.064a.076.076 0 0 0 .084.028c1.978-.626 4.004-1.567 6.107-3.115a.077.077 0 0 0 .03-.055c.5-5.177-.838-9.637-3.548-13.625a.061.061 0 0 0-.031-.028ZM8.02 15.331c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.418 2.157-2.418 1.21 0 2.175 1.094 2.157 2.418 0 1.334-.955 2.419-2.157 2.419Zm7.974 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.418 2.157-2.418 1.21 0 2.175 1.094 2.157 2.418 0 1.334-.947 2.419-2.157 2.419Z" />
            </svg>
          </a>
          <a
            href="https://www.linkedin.com/company/qbraid-official/"
            target="_blank"
            rel="noopener noreferrer"
            className="hover:text-white transition"
          >
            <Linkedin className="h-4 w-4" />
          </a>
          <a
            href="https://github.com/qbraid"
            target="_blank"
            rel="noopener noreferrer"
            className="hover:text-white transition"
          >
            <Github className="h-4 w-4" />
          </a>
        </div>
      </div>
    </footer>
  );
}
