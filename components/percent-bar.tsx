// A reusable component to display a percentage bar with a label and color.
export function PercentBar({
  name,
  percentage,
  color,
}: {
  name: string;
  percentage: string;
  color: string;
}) {
  return (
    <div>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 ${color} rounded-full`}></div>
          <span className="text-[#94a3b8] text-sm">{name}</span>
        </div>
        <span className="text-white font-medium">{percentage}</span>
      </div>
      <div className="w-full bg-[#1d1825] rounded-full h-2">
        <div className={`${color} h-2 rounded-full`} style={{ width: percentage }}></div>
      </div>
    </div>
  );
}
