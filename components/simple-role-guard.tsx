'use client';

import { useUserRole } from '@/hooks/use-api';
import { Loader2 } from 'lucide-react';

interface SimpleRoleGuardProps {
  orgId: string;
  allowedRoles: string[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
  loadingFallback?: React.ReactNode;
}

/**
 * Simple Role Guard Component
 * Uses the new simple role fetching system - much cleaner than complex permissions!
 */
export function SimpleRoleGuard({
  orgId,
  allowedRoles,
  children,
  fallback = null,
  loadingFallback = <Loader2 className="h-4 w-4 animate-spin" />,
}: SimpleRoleGuardProps) {
  const { data: roleData, isLoading, error } = useUserRole(orgId);

  if (isLoading) {
    return <>{loadingFallback}</>;
  }

  if (error) {
    return <>{fallback}</>;
  }

  const userRole = roleData?.data?.role;
  const hasAccess = userRole && allowedRoles.includes(userRole);

  return hasAccess ? <>{children}</> : <>{fallback}</>;
}

/**
 * Simple role checking hooks - use these for conditional logic
 */
export function useSimpleRole(orgId: string) {
  const { data: roleData, isLoading, error } = useUserRole(orgId);

  const userRole = roleData?.data?.role;

  return {
    role: userRole,
    isLoading,
    error,

    // Helper functions
    isOwner: () => userRole === 'owner',
    isAdmin: () => userRole === 'admin',
    isMember: () => userRole === 'member',
    isViewer: () => userRole === 'viewer',
    hasRole: (role: string) => userRole === role,
    hasAnyRole: (roles: string[]) => roles.includes(userRole || ''),

    // Access level checks
    canManage: () => ['owner', 'admin'].includes(userRole || ''),
    canView: () => ['owner', 'admin', 'member', 'viewer'].includes(userRole || ''),
    canDelete: () => ['owner', 'admin'].includes(userRole || ''),
  };
}

/**
 * Usage Examples:
 *
 * // Simple Role Guard
 * <SimpleRoleGuard orgId="123" allowedRoles={['admin', 'owner']}>
 *   <AdminPanel />
 * </SimpleRoleGuard>
 *
 * // Simple Role Hook
 * const { isAdmin, canManage, role } = useSimpleRole('123');
 *
 * if (isAdmin()) {
 *   return <AdminControls />;
 * }
 *
 * if (canManage()) {
 *   return <ManagementTools />;
 * }
 */
