import { memo } from 'react';
import type { ActionLogRowProps } from '@/types/logs';

const LogRowElement = memo(({ field, date }: { field?: string; date?: Date }) => {
  if (!field) {
    field = 'N/A';
  }
  let textSize = 'sm';
  if (date) {
    textSize = 'xs';
    field = date.toLocaleString();
  }

  return (
    <td className="py-3">
      <div className="flex items-center px-4">
        <span className={`text-[#94a3b8] text-${textSize}`}>{field}</span>
      </div>
    </td>
  );
});

export const ActionLogRow = memo(({ ...log }: ActionLogRowProps) => (
  <tr className="border-b border-[#3b3b3b] hover:bg-[#262131]/50">
    <LogRowElement field={log.userEmail} />
    <LogRowElement field={log.userRole} />
    <LogRowElement field={log.action} />
    <LogRowElement field={log.actionDescription} />
    <LogRowElement date={log.createdAt ? new Date(log.createdAt) : undefined} />
  </tr>
));

ActionLogRow.displayName = 'ActionLogRow';
LogRowElement.displayName = 'LogRowElement';
