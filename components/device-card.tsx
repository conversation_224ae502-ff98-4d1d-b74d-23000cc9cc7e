/**
 * DeviceCard component
 * --------------------
 * This module defines the DeviceCard React component, which displays detailed information
 * about a quantum device. It includes tabs for device details, jobs info, qubit topology,
 * and calibration metrics. The jobs info tab fetches and displays job data using DeviceJobsTab.
 * The component is styled for the qBraid partner dashboard and expects device information as props.
 */

'use client';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Download, Edit } from 'lucide-react';
import { QubitTopology } from '@/components/qubit-topology';
import { CalibrationMetrics } from '@/components/calibration-metrics';
import { DeviceJobsTab } from '@/components/device-jobs-tab';
import Link from 'next/link';
import type { DeviceCardProps } from '@/types/device';

export function DeviceCard({
  qbraid_id,
  name,
  type,
  deviceDescription,
  paradigm,
  numberQubits,
  status,
  provider,
  architecture = 'N/A',
  processorType = 'N/A',
  pricing,
  vendor,
  runPackage,
  pendingJobs,
  noiseModels,
  defaultTab = 'details',
}: DeviceCardProps) {
  // Calibration metrics data
  const calibrationMetrics = [
    {
      name: 'Readout Assignment Error',
      median: '1.587e-2',
      min: '6.615e-3',
      max: '2.222e-1',
      color: '#3b82f6',
      value: 40,
    },
    {
      name: 'ECR Error',
      median: '6.560e-3',
      min: '3.206e-3',
      max: '2.481e-1',
      color: '#3b82f6',
      value: 30,
    },
    {
      name: 'SX Error',
      median: '2.219e-4',
      color: '#3b82f6',
      value: 75,
    },
    {
      name: 'T1 Relaxation Time',
      median: '280.82 μs',
      color: '#8b5cf6',
      value: 60,
    },
    {
      name: 'T2 Coherence Time',
      median: '214.87 μs',
      color: '#8b5cf6',
      value: 50,
    },
  ];

  const statusColor = (() => {
    if (!status) return '#94a3b8'; // gray for undefined or null status (retired)
    switch (status.toLowerCase()) {
      case 'active':
      case 'online':
        return '#22c55e'; // green
      case 'offline':
        return '#ef4444'; // red
      default:
        return '#94a3b8'; // gray
    }
  })();

  // how the card will display
  return (
    <Card className="border-0 bg-transparent text-white">
      {/* The header of the quantum device card */}
      <CardHeader className="pb-4 sm:pb-6 px-4 sm:px-6">
        <div className="flex flex-col gap-4">
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3 sm:gap-4">
            <div className="flex-1 min-w-0">
              <h2 className="text-xl sm:text-2xl font-semibold text-white mb-2 break-words">
                {name}
              </h2>
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                <Badge className="bg-[#8a2be2]/90 text-white hover:bg-[#8a2be2] border-0 font-medium px-3 py-1 w-fit transition-all duration-200 hover:scale-105">
                  {type}
                </Badge>
                <div className="flex items-center gap-2">
                  <div
                    className="w-2 h-2 rounded-full flex-shrink-0 animate-pulse"
                    style={{ backgroundColor: statusColor }}
                  ></div>
                  <span className="text-[#94a3b8] text-sm font-medium">{status}</span>
                </div>
              </div>
            </div>
            <div className="flex gap-2 sm:gap-3 flex-shrink-0">
              <Button
                size="sm"
                variant="outline"
                className="bg-[#1f1b24] border-[#374151] text-[#94a3b8] hover:bg-[#2a2631] hover:text-white hover:border-[#4b5563] transition-all duration-300 min-w-0 h-9 hover:scale-105 hover:shadow-lg"
                aria-label="Export"
              >
                <Download className="w-4 h-4 sm:mr-2 transition-transform duration-200 group-hover:scale-110" />
                <span className="hidden sm:inline">Export</span>
              </Button>
              <Link href={`/edit-device?id=${qbraid_id}`}>
                <Button
                  size="sm"
                  className="bg-[#8a2be2] hover:bg-[#7c2dd5] text-white shadow-sm transition-all duration-300 min-w-0 h-9 hover:scale-105 hover:shadow-lg hover:shadow-[#8a2be2]/25"
                  aria-label="Edit Info"
                >
                  <Edit className="w-4 h-4 sm:mr-2 transition-transform duration-200 group-hover:scale-110" />
                  <span className="hidden sm:inline">Edit</span>
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="px-4 sm:px-6 pb-4 sm:pb-6">
        {/* The tab bar on the quantum device card */}
        <Tabs defaultValue={defaultTab} className="w-full">
          <div className="overflow-x-auto -mx-4 sm:mx-0 mb-6 sm:mb-8">
            <TabsList className="bg-[#1f1b24] border border-[#374151] rounded-lg p-1 w-max sm:w-full justify-start h-auto mx-4 sm:mx-0 transition-all duration-200">
              <TabsTrigger
                value="details"
                className="bg-transparent data-[state=active]:bg-[#8a2be2] data-[state=active]:text-white data-[state=active]:shadow-lg text-[#94a3b8] rounded-md px-3 sm:px-4 py-2.5 mr-1 font-medium transition-all duration-300 whitespace-nowrap text-sm hover:text-white hover:bg-[#8a2be2]/20"
              >
                Details
              </TabsTrigger>
              <TabsTrigger
                value="jobs-info"
                className="bg-transparent data-[state=active]:bg-[#8a2be2] data-[state=active]:text-white data-[state=active]:shadow-lg text-[#94a3b8] rounded-md px-3 sm:px-4 py-2.5 mr-1 font-medium transition-all duration-300 whitespace-nowrap text-sm hover:text-white hover:bg-[#8a2be2]/20"
              >
                Jobs
              </TabsTrigger>
              <TabsTrigger
                value="qubit-topology"
                className="bg-transparent data-[state=active]:bg-[#8a2be2] data-[state=active]:text-white data-[state=active]:shadow-lg text-[#94a3b8] rounded-md px-3 sm:px-4 py-2.5 mr-1 font-medium transition-all duration-300 whitespace-nowrap text-sm hover:text-white hover:bg-[#8a2be2]/20"
              >
                Topology
              </TabsTrigger>
              <TabsTrigger
                value="calibration"
                className="bg-transparent data-[state=active]:bg-[#8a2be2] data-[state=active]:text-white data-[state=active]:shadow-lg text-[#94a3b8] rounded-md px-3 sm:px-4 py-2.5 font-medium transition-all duration-300 whitespace-nowrap text-sm hover:text-white hover:bg-[#8a2be2]/20"
              >
                Calibration
              </TabsTrigger>
            </TabsList>
          </div>

          {/* The Device Details tab on the quantum device card */}
          <TabsContent value="details" className="mt-0">
            <div className="space-y-6 sm:space-y-8">
              <div>
                <h3 className="text-lg sm:text-xl font-semibold text-white mb-2 sm:mb-3">
                  Device Details
                </h3>
                <p className="text-[#94a3b8] text-sm sm:text-base leading-relaxed">
                  {deviceDescription}
                </p>
              </div>

              {/* Main metrics grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
                <div className="space-y-4 sm:space-y-6">
                  <div className="bg-[#1f1b24] rounded-lg p-4 border border-[#374151] transition-all duration-300 hover:border-[#4b5563] hover:bg-[#252028] hover:shadow-lg hover:-translate-y-1">
                    <p className="text-[#94a3b8] text-xs sm:text-sm font-medium mb-2 transition-colors duration-200">
                      Qubits
                    </p>
                    <p className="text-white text-2xl sm:text-3xl font-bold transition-all duration-200">
                      {numberQubits}
                    </p>
                  </div>
                  <div className="bg-[#1f1b24] rounded-lg p-4 border border-[#374151] transition-all duration-300 hover:border-[#4b5563] hover:bg-[#252028] hover:shadow-lg hover:-translate-y-1">
                    <p className="text-[#94a3b8] text-xs sm:text-sm font-medium mb-2 transition-colors duration-200">
                      Cost Per Min
                    </p>
                    <p className="text-white text-lg sm:text-xl font-semibold transition-all duration-200">
                      {pricing?.perMinute !== null && pricing?.perMinute !== undefined
                        ? `$${pricing.perMinute}`
                        : 'N/A'}
                    </p>
                  </div>
                </div>

                <div className="space-y-4 sm:space-y-6">
                  <div className="bg-[#1f1b24] rounded-lg p-4 border border-[#374151] transition-all duration-300 hover:border-[#4b5563] hover:bg-[#252028] hover:shadow-lg hover:-translate-y-1">
                    <p className="text-[#94a3b8] text-xs sm:text-sm font-medium mb-2 transition-colors duration-200">
                      Provider
                    </p>
                    <p className="text-white text-base sm:text-lg font-semibold break-words transition-all duration-200">
                      {provider}
                    </p>
                  </div>
                  <div className="bg-[#1f1b24] rounded-lg p-4 border border-[#374151] transition-all duration-300 hover:border-[#4b5563] hover:bg-[#252028] hover:shadow-lg hover:-translate-y-1">
                    <p className="text-[#94a3b8] text-xs sm:text-sm font-medium mb-2 transition-colors duration-200">
                      Cost Per Shot
                    </p>
                    <p className="text-white text-lg sm:text-xl font-semibold transition-all duration-200">
                      {pricing?.perShot !== null && pricing?.perShot !== undefined
                        ? `$${pricing.perShot}`
                        : 'N/A'}
                    </p>
                  </div>
                </div>

                <div className="space-y-4 sm:space-y-6">
                  <div className="bg-[#1f1b24] rounded-lg p-4 border border-[#374151] transition-all duration-300 hover:border-[#4b5563] hover:bg-[#252028] hover:shadow-lg hover:-translate-y-1">
                    <p className="text-[#94a3b8] text-xs sm:text-sm font-medium mb-2 transition-colors duration-200">
                      Vendor
                    </p>
                    <p className="text-white text-base sm:text-lg font-semibold break-words transition-all duration-200">
                      {vendor}
                    </p>
                  </div>
                  <div className="bg-[#1f1b24] rounded-lg p-4 border border-[#374151] transition-all duration-300 hover:border-[#4b5563] hover:bg-[#252028] hover:shadow-lg hover:-translate-y-1">
                    <p className="text-[#94a3b8] text-xs sm:text-sm font-medium mb-2 transition-colors duration-200">
                      Cost Per Task
                    </p>
                    <p className="text-white text-lg sm:text-xl font-semibold transition-all duration-200">
                      {pricing?.perTask !== null && pricing?.perTask !== undefined
                        ? `$${pricing.perTask}`
                        : 'N/A'}
                    </p>
                  </div>
                </div>

                <div className="space-y-4 sm:space-y-6">
                  <div className="bg-[#1f1b24] rounded-lg p-4 border border-[#374151] transition-all duration-300 hover:border-[#4b5563] hover:bg-[#252028] hover:shadow-lg hover:-translate-y-1">
                    <p className="text-[#94a3b8] text-xs sm:text-sm font-medium mb-2 transition-colors duration-200">
                      {type === 'Simulator' ? 'Processor Type' : 'Architecture'}
                    </p>
                    <p className="text-white text-base sm:text-lg font-semibold break-words transition-all duration-200">
                      {type === 'Simulator' ? processorType || 'N/A' : architecture || 'N/A'}
                    </p>
                  </div>
                  <div className="bg-[#1f1b24] rounded-lg p-4 border border-[#374151] transition-all duration-300 hover:border-[#4b5563] hover:bg-[#252028] hover:shadow-lg hover:-translate-y-1">
                    <p className="text-[#94a3b8] text-xs sm:text-sm font-medium mb-2 transition-colors duration-200">
                      Paradigm
                    </p>
                    <p className="text-white text-base sm:text-lg font-semibold break-words transition-all duration-200">
                      {paradigm}
                    </p>
                  </div>
                </div>
              </div>

              {/* Additional info grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                <div className="bg-[#1f1b24] rounded-lg p-4 border border-[#374151] transition-all duration-300 hover:border-[#4b5563] hover:bg-[#252028] hover:shadow-lg hover:-translate-y-1">
                  <p className="text-[#94a3b8] text-xs sm:text-sm font-medium mb-2 transition-colors duration-200">
                    Run Package
                  </p>
                  <p className="text-white text-sm sm:text-base font-semibold break-words transition-all duration-200">
                    {runPackage}
                  </p>
                </div>
                <div className="bg-[#1f1b24] rounded-lg p-4 border border-[#374151] transition-all duration-300 hover:border-[#4b5563] hover:bg-[#252028] hover:shadow-lg hover:-translate-y-1">
                  <p className="text-[#94a3b8] text-xs sm:text-sm font-medium mb-2 transition-colors duration-200">
                    Pending Jobs
                  </p>
                  <p className="text-white text-sm sm:text-base font-semibold transition-all duration-200">
                    {pendingJobs ?? 'N/A'}
                  </p>
                </div>
                <div className="bg-[#1f1b24] rounded-lg p-4 border border-[#374151] sm:col-span-2 lg:col-span-1 transition-all duration-300 hover:border-[#4b5563] hover:bg-[#252028] hover:shadow-lg hover:-translate-y-1">
                  <p className="text-[#94a3b8] text-xs sm:text-sm font-medium mb-2 transition-colors duration-200">
                    Noise Model
                  </p>
                  <p className="text-white text-sm sm:text-base font-semibold break-words transition-all duration-200">
                    {noiseModels && noiseModels.length > 0 ? noiseModels.join(', ') : 'N/A'}
                  </p>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* The Jobs Info tab on the quantum device card */}
          <TabsContent value="jobs-info" className="mt-0">
            <DeviceJobsTab></DeviceJobsTab>
          </TabsContent>

          {/* The Qubit Topology tab on the quantum device card */}
          <TabsContent value="qubit-topology" className="mt-0">
            <QubitTopology
              qubits={parseInt(numberQubits, 10)}
              error2QBest="3.36e-3"
              error2QLayered="3.90e-2"
              clops="249K"
            />
          </TabsContent>

          {/* The Calibration tab on the quantum device card */}
          <TabsContent value="calibration" className="mt-0">
            <CalibrationMetrics metrics={calibrationMetrics} />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
