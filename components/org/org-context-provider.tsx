'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { usePermissions, type OrgRole } from '@/hooks/use-permissions';
import { useQueryClient } from '@tanstack/react-query';

interface OrgContextType {
  currentOrgId: string | null;
  currentOrg: OrgRole | null;
  organizations: OrgRole[];
  switchOrganization: (orgId: string) => void;
  isLoading: boolean;
  error: string | null;
  _updateCounter?: number; // Internal counter to force re-renders
}

const OrgContext = createContext<OrgContextType | null>(null);

/**
 * Organization context provider that manages current organization state
 * and provides reactive updates when organization changes
 */
export function OrgContextProvider({ children }: { children: React.ReactNode }) {
  const {
    getOrganizations,
    getCurrentOrgContext,
    setCurrentOrgContext,
    loading,
    error,
    refreshPermissions,
  } = usePermissions();

  const [currentOrgId, setCurrentOrgId] = useState<string | null>(null);
  const [isChanging, setIsChanging] = useState(false);
  const [updateCounter, setUpdateCounter] = useState(0);
  const queryClient = useQueryClient();

  // Force update function to trigger re-renders
  const forceUpdate = () => {
    setUpdateCounter((prev) => prev + 1);
  };

  const organizations = getOrganizations();
  const currentOrg = currentOrgId
    ? organizations.find((org) => org.orgId === currentOrgId) || null
    : null;

  // Initialize current org from permissions hook
  useEffect(() => {
    const orgId = getCurrentOrgContext();
    if (orgId !== currentOrgId) {
      setCurrentOrgId(orgId);
      console.log(`🏢 [ORG-CONTEXT] Initialized org context:`, {
        orgId,
        previousOrgId: currentOrgId,
      });
    }
  }, [getCurrentOrgContext, currentOrgId]);

  // Force re-render when organizations data changes
  useEffect(() => {
    if (organizations.length > 0 && !currentOrgId) {
      const defaultOrgId = getCurrentOrgContext();
      if (defaultOrgId) {
        setCurrentOrgId(defaultOrgId);
        console.log(`🏢 [ORG-CONTEXT] Set default org:`, { defaultOrgId });
      }
    }
  }, [organizations, currentOrgId, getCurrentOrgContext]);

  // Listen for organization context changes from other components
  useEffect(() => {
    const handleOrgChange = (event: CustomEvent) => {
      const { orgId } = event.detail;
      if (orgId !== currentOrgId) {
        setCurrentOrgId(orgId);
        console.log(`🏢 [ORG-CONTEXT] Organization changed via event:`, {
          newOrgId: orgId,
          previousOrgId: currentOrgId,
        });
      }
    };

    window.addEventListener('org-context-changed', handleOrgChange as EventListener);
    return () => {
      window.removeEventListener('org-context-changed', handleOrgChange as EventListener);
    };
  }, [currentOrgId]);

  const switchOrganization = async (orgId: string) => {
    if (orgId === currentOrgId) return;

    setIsChanging(true);
    try {
      const targetOrg = organizations.find((org) => org.orgId === orgId);

      console.log(`🔄 [ORG-CONTEXT] Starting organization switch:`, {
        fromOrgId: currentOrgId,
        toOrgId: orgId,
        targetOrgName: targetOrg?.orgName,
      });

      // 1. Update localStorage and context first
      setCurrentOrgContext(orgId);
      setCurrentOrgId(orgId);

      // 2. Invalidate all permission-related queries to force refresh
      await queryClient.invalidateQueries({
        queryKey: ['permissions'],
      });

      // 3. Invalidate enhanced role cache queries
      await queryClient.invalidateQueries({
        queryKey: ['userOrgRole'],
      });

      // 4. Refresh permissions to get new role data
      refreshPermissions();

      // 5. Force update all components
      forceUpdate();

      // 6. Broadcast change with more details
      window.dispatchEvent(
        new CustomEvent('org-context-changed', {
          detail: {
            orgId,
            org: targetOrg,
            previousOrgId: currentOrgId,
            timestamp: Date.now(),
          },
        }),
      );

      console.log(`✅ [ORG-CONTEXT] Organization switched successfully:`, {
        newOrgId: orgId,
        newOrgName: targetOrg?.orgName,
        previousOrgId: currentOrgId,
        cacheInvalidated: true,
        updateCounter: updateCounter + 1,
      });
    } catch (error) {
      console.error(`❌ [ORG-CONTEXT] Failed to switch organization:`, error);
    } finally {
      setIsChanging(false);
    }
  };

  const contextValue: OrgContextType = {
    currentOrgId,
    currentOrg,
    organizations,
    switchOrganization,
    isLoading: loading || isChanging,
    error: error,
    // Include updateCounter to force re-renders
    _updateCounter: updateCounter,
  };

  return <OrgContext.Provider value={contextValue}>{children}</OrgContext.Provider>;
}

/**
 * Hook to access organization context
 */
export function useOrgContext(): OrgContextType {
  const context = useContext(OrgContext);

  if (!context) {
    throw new Error('useOrgContext must be used within an OrgContextProvider');
  }

  return context;
}

/**
 * HOC for components that need organization context
 */
export function withOrgContext<P extends object>(Component: React.ComponentType<P>) {
  return function OrgContextWrappedComponent(props: P) {
    return (
      <OrgContextProvider>
        <Component {...props} />
      </OrgContextProvider>
    );
  };
}

/**
 * Organization selector component for easy switching
 */
interface OrgSelectorProps {
  className?: string;
  showRole?: boolean;
  compact?: boolean;
}

export function OrgSelector({
  className = '',
  showRole = true,
  compact = false,
}: OrgSelectorProps) {
  const { currentOrg, organizations, switchOrganization, isLoading } = useOrgContext();

  if (organizations.length <= 1) {
    return null; // Don't show selector if only one or no organizations
  }

  return (
    <div className={`org-selector ${className}`}>
      <select
        value={currentOrg?.orgId || ''}
        onChange={(e) => switchOrganization(e.target.value)}
        disabled={isLoading}
        className={`
          border border-gray-300 rounded-md px-3 py-2 text-sm
          ${compact ? 'px-2 py-1 text-xs' : ''}
          ${isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
      >
        {organizations.map((org) => (
          <option key={org.orgId} value={org.orgId}>
            {org.orgName} {showRole ? `(${org.role})` : ''}
          </option>
        ))}
      </select>
      {isLoading && <span className="ml-2 text-xs text-gray-500">Switching...</span>}
    </div>
  );
}

/**
 * Organization info display component
 */
interface OrgInfoProps {
  className?: string;
  showRole?: boolean;
  showCount?: boolean;
}

export function OrgInfo({ className = '', showRole = true, showCount = false }: OrgInfoProps) {
  const { currentOrg, organizations } = useOrgContext();

  if (!currentOrg) {
    return <div className={`text-gray-500 text-sm ${className}`}>No organization selected</div>;
  }

  return (
    <div className={`org-info ${className}`}>
      <span className="font-medium">{currentOrg.orgName}</span>
      {showRole && <span className="text-gray-500 ml-2">({currentOrg.role})</span>}
      {showCount && organizations.length > 1 && (
        <span className="text-gray-400 ml-2 text-xs">{organizations.length} orgs</span>
      )}
    </div>
  );
}
