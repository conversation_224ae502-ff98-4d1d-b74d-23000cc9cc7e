'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { usePermissions, useEnhancedOrgRole, type OrgRole } from '@/hooks/use-permissions';
import { useQueryClient } from '@tanstack/react-query';

interface OrgContextType {
  currentOrgId: string | null;
  currentOrg: OrgRole | null;
  organizations: OrgRole[];
  switchOrganization: (orgId: string) => void;
  isLoading: boolean;
  error: string | null;
}

const OrgContext = createContext<OrgContextType | null>(null);

/**
 * Organization context provider that manages current organization state
 * and provides reactive updates when organization changes
 */
export function OrgContextProvider({ children }: { children: React.ReactNode }) {
  const { getOrganizations, getCurrentOrgContext, setCurrentOrgContext, loading, error } =
    usePermissions();

  const [currentOrgId, setCurrentOrgId] = useState<string | null>(null);
  const [isChanging, setIsChanging] = useState(false);

  const organizations = getOrganizations();
  const currentOrg = currentOrgId
    ? organizations.find((org) => org.orgId === currentOrgId) || null
    : null;

  // Initialize current org from permissions hook
  useEffect(() => {
    const orgId = getCurrentOrgContext();
    if (orgId !== currentOrgId) {
      setCurrentOrgId(orgId);
    }
  }, [getCurrentOrgContext, currentOrgId]);

  // Listen for organization context changes from other components
  useEffect(() => {
    const handleOrgChange = (event: CustomEvent) => {
      const { orgId } = event.detail;
      if (orgId !== currentOrgId) {
        setCurrentOrgId(orgId);
        console.log(`🏢 [ORG-CONTEXT] Organization changed via event:`, {
          newOrgId: orgId,
          previousOrgId: currentOrgId,
        });
      }
    };

    window.addEventListener('org-context-changed', handleOrgChange as EventListener);
    return () => {
      window.removeEventListener('org-context-changed', handleOrgChange as EventListener);
    };
  }, [currentOrgId]);

  const switchOrganization = async (orgId: string) => {
    if (orgId === currentOrgId) return;

    setIsChanging(true);
    try {
      // Enhanced: Prefetch role data for the new organization
      const targetOrg = organizations.find((org) => org.orgId === orgId);

      if (targetOrg) {
        console.log(`🔄 [ORG-CONTEXT] Switching with enhanced caching:`, {
          newOrgId: orgId,
          newOrgName: targetOrg.orgName,
          previousOrgId: currentOrgId,
          cacheKey: `user:${orgId}`, // Enhanced cache structure
        });
      }

      setCurrentOrgContext(orgId);
      setCurrentOrgId(orgId);

      console.log(`✅ [ORG-CONTEXT] Organization switched with enhanced caching:`, {
        newOrgId: orgId,
        newOrgName: targetOrg?.orgName,
        previousOrgId: currentOrgId,
        enhancedCaching: true,
      });
    } catch (error) {
      console.error(`❌ [ORG-CONTEXT] Failed to switch organization:`, error);
    } finally {
      setIsChanging(false);
    }
  };

  const contextValue: OrgContextType = {
    currentOrgId,
    currentOrg,
    organizations,
    switchOrganization,
    isLoading: loading || isChanging,
    error: error,
  };

  return <OrgContext.Provider value={contextValue}>{children}</OrgContext.Provider>;
}

/**
 * Hook to access organization context
 */
export function useOrgContext(): OrgContextType {
  const context = useContext(OrgContext);

  if (!context) {
    throw new Error('useOrgContext must be used within an OrgContextProvider');
  }

  return context;
}

/**
 * HOC for components that need organization context
 */
export function withOrgContext<P extends object>(Component: React.ComponentType<P>) {
  return function OrgContextWrappedComponent(props: P) {
    return (
      <OrgContextProvider>
        <Component {...props} />
      </OrgContextProvider>
    );
  };
}

/**
 * Organization selector component for easy switching
 */
interface OrgSelectorProps {
  className?: string;
  showRole?: boolean;
  compact?: boolean;
}

export function OrgSelector({
  className = '',
  showRole = true,
  compact = false,
}: OrgSelectorProps) {
  const { currentOrg, organizations, switchOrganization, isLoading } = useOrgContext();

  if (organizations.length <= 1) {
    return null; // Don't show selector if only one or no organizations
  }

  return (
    <div className={`org-selector ${className}`}>
      <select
        value={currentOrg?.orgId || ''}
        onChange={(e) => switchOrganization(e.target.value)}
        disabled={isLoading}
        className={`
          border border-gray-300 rounded-md px-3 py-2 text-sm
          ${compact ? 'px-2 py-1 text-xs' : ''}
          ${isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
      >
        {organizations.map((org) => (
          <option key={org.orgId} value={org.orgId}>
            {org.orgName} {showRole ? `(${org.role})` : ''}
          </option>
        ))}
      </select>
      {isLoading && <span className="ml-2 text-xs text-gray-500">Switching...</span>}
    </div>
  );
}

/**
 * Organization info display component
 */
interface OrgInfoProps {
  className?: string;
  showRole?: boolean;
  showCount?: boolean;
}

export function OrgInfo({ className = '', showRole = true, showCount = false }: OrgInfoProps) {
  const { currentOrg, organizations } = useOrgContext();

  if (!currentOrg) {
    return <div className={`text-gray-500 text-sm ${className}`}>No organization selected</div>;
  }

  return (
    <div className={`org-info ${className}`}>
      <span className="font-medium">{currentOrg.orgName}</span>
      {showRole && <span className="text-gray-500 ml-2">({currentOrg.role})</span>}
      {showCount && organizations.length > 1 && (
        <span className="text-gray-400 ml-2 text-xs">{organizations.length} orgs</span>
      )}
    </div>
  );
}
