'use client';

import { useState, useCallback } from 'react';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { Lock, Key, Eye, EyeOff, CheckCircle, AlertCircle, ArrowLeft, Mail } from 'lucide-react';
import Link from 'next/link';
import { completePasswordReset } from '@/app/(auth)/actions';
import { useCSRFToken, CSRFTokenInput } from '@/lib/csrf';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp';

interface PasswordValidation {
  minLength: boolean;
  hasNumber: boolean;
  hasSpecial: boolean;
}

interface FormState {
  email: string;
  code: string;
  password: string;
  confirmPassword: string;
  showPassword: boolean;
  showConfirmPassword: boolean;
  isEmailEditable: boolean;
  initialized: boolean;
}

const INITIAL_FORM_STATE: FormState = {
  email: '',
  code: '',
  password: '',
  confirmPassword: '',
  showPassword: false,
  showConfirmPassword: false,
  isEmailEditable: false,
  initialized: false,
};

export function ResetPasswordForm() {
  const { csrfToken, loading: csrfLoading, error: csrfError } = useCSRFToken();
  const router = useRouter();
  const [formState, setFormState] = useState<FormState>(() => {
    // Initialize with stored email if available
    if (typeof window !== 'undefined') {
      const storedEmail = sessionStorage.getItem('reset-email');
      return {
        ...INITIAL_FORM_STATE,
        email: storedEmail || '',
        isEmailEditable: !storedEmail,
        initialized: true,
      };
    }
    return INITIAL_FORM_STATE;
  });

  // React Query mutation for password reset completion
  const resetMutation = useMutation({
    mutationFn: async (formData: {
      email: string;
      code: string;
      newPassword: string;
      confirmPassword: string;
      csrfToken: string;
    }) => {
      const formDataObj = new FormData();
      formDataObj.set('email', formData.email);
      formDataObj.set('code', formData.code);
      formDataObj.set('newPassword', formData.newPassword);
      formDataObj.set('confirmPassword', formData.confirmPassword);
      formDataObj.set('csrfToken', formData.csrfToken);

      const result = await completePasswordReset(null, formDataObj);
      if (!result.success) {
        throw new Error(result.error || 'Failed to reset password');
      }
      return result;
    },
    onSuccess: () => {
      // Clear the stored email from sessionStorage
      if (typeof window !== 'undefined') {
        sessionStorage.removeItem('reset-email');
      }
      // Redirect to sign-in with success message
      router.push(
        '/signin?message=Password reset successful. Please sign in with your new password.',
      );
    },
  });

  // Initialize form on client side
  if (typeof window !== 'undefined' && !formState.initialized) {
    const storedEmail = sessionStorage.getItem('reset-email');
    setFormState((prev) => ({
      ...prev,
      email: storedEmail || '',
      isEmailEditable: !storedEmail,
      initialized: true,
    }));
  }

  // Validate password requirements
  const validatePassword = useCallback(
    (pwd: string): PasswordValidation => ({
      minLength: pwd.length >= 8,
      hasNumber: /\d/.test(pwd),
      hasSpecial: /[!@#$%^&*(),.?":{}|<>]/.test(pwd),
    }),
    [],
  );

  // Update form state helper
  const updateFormState = useCallback((updates: Partial<FormState>) => {
    setFormState((prev) => ({ ...prev, ...updates }));
  }, []);

  // Form validation
  const passwordValidation = validatePassword(formState.password);
  const isPasswordValid = Object.values(passwordValidation).every(Boolean);
  const passwordsMatch =
    formState.password === formState.confirmPassword && formState.confirmPassword.length > 0;
  const isEmailValid = formState.email.includes('@') && formState.email.includes('.');
  const canSubmit =
    isPasswordValid &&
    passwordsMatch &&
    formState.code.length === 6 &&
    isEmailValid &&
    !resetMutation.isPending;

  const displayError = resetMutation.error?.message || csrfError;

  const handleSubmit = useCallback(
    (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      if (!canSubmit || !csrfToken) return;

      resetMutation.mutate({
        email: formState.email,
        code: formState.code,
        newPassword: formState.password,
        confirmPassword: formState.confirmPassword,
        csrfToken,
      });
    },
    [canSubmit, csrfToken, formState, resetMutation],
  );

  // Show loading state during initialization
  if (!formState.initialized) {
    return (
      <div className="w-full max-w-md h-96 bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] rounded-3xl shadow-2xl border border-purple-800/20 backdrop-blur-sm animate-pulse">
        <div className="p-8 flex items-center justify-center h-full">
          <div className="w-6 h-6 border-2 border-purple-500/30 border-t-purple-500 rounded-full animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] rounded-3xl shadow-2xl border border-purple-800/20 backdrop-blur-sm">
      <div className="p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 mb-6 mx-auto shadow-lg shadow-purple-500/25">
            <Key className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">Reset Password</h1>
          <p className="text-slate-400 text-sm">
            {formState.email && !formState.isEmailEditable
              ? 'Enter the code and your new password'
              : 'Enter your email, reset code, and new password'}
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-5">
          <CSRFTokenInput csrfToken={csrfToken} />

          {/* Error Alert */}
          {displayError && (
            <div className="flex items-start gap-3 p-4 rounded-xl bg-red-500/10 border border-red-500/20 backdrop-blur-sm">
              <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
              <p className="text-red-400 text-sm">{displayError}</p>
            </div>
          )}

          {/* Info Alert for manual email entry */}
          {formState.isEmailEditable && (
            <div className="flex items-start gap-3 p-4 rounded-xl bg-blue-500/10 border border-blue-500/20 backdrop-blur-sm">
              <AlertCircle className="w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <p className="text-blue-400 text-sm">
                  Enter the email address where you want to receive the reset code, then the code
                  and new password.
                </p>
                <p className="text-blue-300 text-xs mt-1">
                  If you haven't requested a reset code yet,{' '}
                  <Link href="/forgot-password" className="underline hover:no-underline">
                    click here to get one
                  </Link>
                  .
                </p>
              </div>
            </div>
          )}

          {/* Email Address */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label htmlFor="email" className="text-sm font-medium text-slate-300 block">
                Email Address
              </label>
              {!formState.isEmailEditable && formState.email && (
                <button
                  type="button"
                  onClick={() => updateFormState({ isEmailEditable: true })}
                  className="text-xs text-purple-400 hover:text-purple-300 hover:underline transition-colors"
                  aria-label="Change email address"
                >
                  Change email?
                </button>
              )}
            </div>
            <div className="relative group">
              <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-slate-500 group-focus-within:text-purple-400 transition-colors" />
              <input
                id="email"
                type="email"
                name="email"
                placeholder={
                  formState.isEmailEditable ? 'Enter your email address' : 'Email address'
                }
                autoComplete="email"
                value={formState.email}
                onChange={(e) => updateFormState({ email: e.target.value })}
                readOnly={!formState.isEmailEditable}
                required
                disabled={resetMutation.isPending}
                className={`w-full pl-10 pr-4 py-3.5 rounded-xl border border-slate-600/50 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500 bg-slate-800/50 text-white placeholder:text-slate-500 transition-all duration-200 hover:border-slate-500 disabled:opacity-50 disabled:cursor-not-allowed ${
                  !formState.isEmailEditable ? 'bg-slate-700/50 cursor-default' : ''
                }`}
                aria-describedby={!isEmailValid && formState.email ? 'email-error' : undefined}
              />
            </div>

            {/* Email validation feedback */}
            {formState.email && !isEmailValid && formState.isEmailEditable && (
              <p id="email-error" className="text-xs text-red-400 flex items-center gap-1">
                <AlertCircle className="w-3 h-3" />
                Please enter a valid email address
              </p>
            )}
          </div>

          {/* Reset Code */}
          <div className="space-y-2">
            <label htmlFor="code" className="text-sm font-medium text-slate-300 block">
              6-Digit Reset Code
            </label>
            <div className="flex justify-center">
              <InputOTP
                value={formState.code}
                onChange={(value) => updateFormState({ code: value })}
                maxLength={6}
                disabled={resetMutation.isPending}
                className="gap-2"
              >
                <InputOTPGroup className="gap-2">
                  <InputOTPSlot
                    index={0}
                    className="w-12 h-12 text-lg font-semibold border-slate-600/50 bg-slate-800/50 text-white focus:border-purple-500 focus:ring-2 focus:ring-purple-500/50 rounded-lg transition-all duration-200 hover:border-slate-500"
                  />
                  <InputOTPSlot
                    index={1}
                    className="w-12 h-12 text-lg font-semibold border-slate-600/50 bg-slate-800/50 text-white focus:border-purple-500 focus:ring-2 focus:ring-purple-500/50 rounded-lg transition-all duration-200 hover:border-slate-500"
                  />
                  <InputOTPSlot
                    index={2}
                    className="w-12 h-12 text-lg font-semibold border-slate-600/50 bg-slate-800/50 text-white focus:border-purple-500 focus:ring-2 focus:ring-purple-500/50 rounded-lg transition-all duration-200 hover:border-slate-500"
                  />
                </InputOTPGroup>
                <div className="w-4 flex justify-center">
                  <div className="w-2 h-0.5 bg-slate-600"></div>
                </div>
                <InputOTPGroup className="gap-2">
                  <InputOTPSlot
                    index={3}
                    className="w-12 h-12 text-lg font-semibold border-slate-600/50 bg-slate-800/50 text-white focus:border-purple-500 focus:ring-2 focus:ring-purple-500/50 rounded-lg transition-all duration-200 hover:border-slate-500"
                  />
                  <InputOTPSlot
                    index={4}
                    className="w-12 h-12 text-lg font-semibold border-slate-600/50 bg-slate-800/50 text-white focus:border-purple-500 focus:ring-2 focus:ring-purple-500/50 rounded-lg transition-all duration-200 hover:border-slate-500"
                  />
                  <InputOTPSlot
                    index={5}
                    className="w-12 h-12 text-lg font-semibold border-slate-600/50 bg-slate-800/50 text-white focus:border-purple-500 focus:ring-2 focus:ring-purple-500/50 rounded-lg transition-all duration-200 hover:border-slate-500"
                  />
                </InputOTPGroup>
              </InputOTP>
            </div>

            {/* Code validation feedback */}
            {formState.code.length > 0 && formState.code.length < 6 && (
              <p className="text-xs text-amber-400 flex items-center gap-1 justify-center">
                <AlertCircle className="w-3 h-3" />
                Enter all 6 digits
              </p>
            )}

            {formState.code.length === 6 && (
              <p className="text-xs text-green-400 flex items-center gap-1 justify-center">
                <CheckCircle className="w-3 h-3" />
                Code complete
              </p>
            )}
          </div>

          {/* New Password */}
          <div className="space-y-2">
            <label htmlFor="password" className="text-sm font-medium text-slate-300 block">
              New Password
            </label>
            <div className="relative group">
              <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-slate-500 group-focus-within:text-purple-400 transition-colors" />
              <input
                id="password"
                name="password"
                type={formState.showPassword ? 'text' : 'password'}
                placeholder="Enter your new password"
                autoComplete="new-password"
                value={formState.password}
                onChange={(e) => updateFormState({ password: e.target.value })}
                required
                disabled={resetMutation.isPending}
                className="w-full pl-10 pr-12 py-3.5 rounded-xl border border-slate-600/50 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500 bg-slate-800/50 text-white placeholder:text-slate-500 transition-all duration-200 hover:border-slate-500 disabled:opacity-50 disabled:cursor-not-allowed"
                aria-describedby="password-requirements"
              />
              <button
                type="button"
                onClick={() => updateFormState({ showPassword: !formState.showPassword })}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-500 hover:text-purple-400 transition-colors"
                aria-label={formState.showPassword ? 'Hide password' : 'Show password'}
              >
                {formState.showPassword ? (
                  <EyeOff className="w-4 h-4" />
                ) : (
                  <Eye className="w-4 h-4" />
                )}
              </button>
            </div>

            {/* Password requirements */}
            {formState.password && (
              <div
                id="password-requirements"
                className="mt-2 p-3 bg-slate-800/30 rounded-lg border border-slate-700/50"
              >
                <p className="text-xs text-slate-400 mb-2">Password requirements:</p>
                <div className="space-y-1">
                  <div
                    className={`flex items-center gap-2 text-xs ${passwordValidation.minLength ? 'text-green-400' : 'text-slate-500'}`}
                  >
                    <CheckCircle className="w-3 h-3" />
                    At least 8 characters
                  </div>
                  <div
                    className={`flex items-center gap-2 text-xs ${passwordValidation.hasNumber ? 'text-green-400' : 'text-slate-500'}`}
                  >
                    <CheckCircle className="w-3 h-3" />
                    Contains a number
                  </div>
                  <div
                    className={`flex items-center gap-2 text-xs ${passwordValidation.hasSpecial ? 'text-green-400' : 'text-slate-500'}`}
                  >
                    <CheckCircle className="w-3 h-3" />
                    Contains a special character
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Confirm Password */}
          <div className="space-y-2">
            <label htmlFor="confirmPassword" className="text-sm font-medium text-slate-300 block">
              Confirm New Password
            </label>
            <div className="relative group">
              <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-slate-500 group-focus-within:text-purple-400 transition-colors" />
              <input
                id="confirmPassword"
                name="confirmPassword"
                type={formState.showConfirmPassword ? 'text' : 'password'}
                placeholder="Confirm your new password"
                autoComplete="new-password"
                value={formState.confirmPassword}
                onChange={(e) => updateFormState({ confirmPassword: e.target.value })}
                required
                disabled={resetMutation.isPending}
                className="w-full pl-10 pr-12 py-3.5 rounded-xl border border-slate-600/50 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500 bg-slate-800/50 text-white placeholder:text-slate-500 transition-all duration-200 hover:border-slate-500 disabled:opacity-50 disabled:cursor-not-allowed"
                aria-describedby={
                  !passwordsMatch && formState.confirmPassword
                    ? 'confirm-password-error'
                    : undefined
                }
              />
              <button
                type="button"
                onClick={() =>
                  updateFormState({ showConfirmPassword: !formState.showConfirmPassword })
                }
                className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-500 hover:text-purple-400 transition-colors"
                aria-label={
                  formState.showConfirmPassword ? 'Hide confirm password' : 'Show confirm password'
                }
              >
                {formState.showConfirmPassword ? (
                  <EyeOff className="w-4 h-4" />
                ) : (
                  <Eye className="w-4 h-4" />
                )}
              </button>
            </div>

            {/* Password matching validation */}
            {formState.confirmPassword && !passwordsMatch && (
              <p
                id="confirm-password-error"
                className="text-xs text-red-400 flex items-center gap-1"
              >
                <AlertCircle className="w-3 h-3" />
                Passwords do not match
              </p>
            )}

            {formState.confirmPassword && passwordsMatch && (
              <p className="text-xs text-green-400 flex items-center gap-1">
                <CheckCircle className="w-3 h-3" />
                Passwords match
              </p>
            )}
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={csrfLoading || !csrfToken || !canSubmit}
            className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-semibold py-4 rounded-xl shadow-lg hover:shadow-xl hover:shadow-purple-500/25 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.01] active:scale-[0.99] mt-6 disabled:hover:scale-100"
            aria-label="Reset password"
          >
            {csrfLoading || resetMutation.isPending ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                {csrfLoading ? 'Loading...' : 'Resetting Password...'}
              </div>
            ) : (
              <>
                <Key className="w-4 h-4 mr-2 inline" aria-hidden="true" />
                Reset Password
              </>
            )}
          </button>
        </form>

        {/* Navigation Links */}
        <div className="mt-8 space-y-4">
          {/* Back to Sign In */}
          <div className="flex items-center justify-center gap-2 text-center text-sm text-slate-400">
            <ArrowLeft className="w-4 h-4" aria-hidden="true" />
            <span>Back to</span>
            <Link
              href="/signin"
              className="text-purple-400 hover:text-purple-300 font-medium underline underline-offset-2 transition-colors duration-200"
            >
              Sign In
            </Link>
          </div>

          {/* Try different email */}
          <div className="text-center text-sm text-slate-500">
            Need to use a different email?{' '}
            <Link
              href="/forgot-password"
              className="text-purple-400 hover:text-purple-300 font-medium underline underline-offset-2 transition-colors duration-200"
            >
              Start over
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
