'use client';

import { useActionState, useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { CheckCircle, Mail, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { verifyEmail } from '@/app/(auth)/actions';
import { useCSRFToken, CSRFTokenInput } from '../../lib/csrf';
import { AuthResult } from '@/types/auth';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp';
import { useAuth } from './auth-provider';

const initialState: AuthResult = {
  success: false,
  error: undefined,
};

export function VerifyForm() {
  const [state, formAction] = useActionState(verifyEmail, initialState);
  const { csrfToken, loading: csrfLoading, error: csrfError } = useCSRFToken();
  const { handleAuthSuccess } = useAuth();
  const searchParams = useSearchParams();
  const [code, setCode] = useState('');

  // Get email directly from URL parameters (no useEffect)
  const emailParam = searchParams.get('email');
  const email = emailParam ? decodeURIComponent(emailParam) : '';

  // Handle success with useEffect to avoid render-time router updates
  useEffect(() => {
    if (state.success) {
      const redirectPath = state.redirectTo || '/signin';
      handleAuthSuccess(state, redirectPath);
    }
  }, [state.success, state.redirectTo, handleAuthSuccess]);

  const displayError = state.error || csrfError;

  const handleSubmit = (formData: FormData) => {
    // Add the OTP code to the form data
    formData.set('code', code);
    formAction(formData);
  };

  return (
    <div className="w-full bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] rounded-3xl shadow-2xl border border-purple-800/20 backdrop-blur-sm">
      <div className="p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 mb-6 mx-auto shadow-lg shadow-purple-500/25">
            <CheckCircle className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">Verify Email</h1>
          <p className="text-slate-400 text-sm">Enter the verification code sent to your email</p>
        </div>

        {/* Form */}
        <form action={handleSubmit} className="space-y-5">
          <CSRFTokenInput csrfToken={csrfToken} />

          {/* Error Alert */}
          {displayError && (
            <div className="flex items-start gap-3 p-4 rounded-xl bg-red-500/10 border border-red-500/20 backdrop-blur-sm">
              <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
              <p className="text-red-400 text-sm">{displayError}</p>
            </div>
          )}

          {/* Success message if redirectTo is provided */}
          {state.error && state.redirectTo && (
            <div className="flex items-start gap-3 p-4 rounded-xl bg-green-500/10 border border-green-500/20 backdrop-blur-sm">
              <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0 mt-0.5" />
              <p className="text-green-400 text-sm">{state.error}</p>
            </div>
          )}

          {/* Email Address */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-slate-300 block">Email Address</label>
            <div className="relative group">
              <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-slate-500" />
              <input
                type="email"
                value={email}
                disabled
                className="w-full pl-10 pr-4 py-3.5 rounded-xl border border-slate-600/50 bg-slate-800/30 text-slate-400 cursor-not-allowed"
              />
              {/* Hidden input to ensure email is submitted */}
              <input type="hidden" name="email" value={email} />
            </div>
            {email && (
              <p className="text-xs text-slate-500 flex items-center gap-1">
                <CheckCircle className="w-3 h-3" />
                Verification code sent to this email address
              </p>
            )}
          </div>

          {/* Verification Code */}
          <div className="space-y-3">
            <label className="text-sm font-medium text-slate-300 block">Verification Code</label>
            <div className="flex flex-col items-center gap-3">
              <InputOTP
                maxLength={6}
                value={code}
                onChange={setCode}
                className="gap-3"
                containerClassName="group"
              >
                <InputOTPGroup className="gap-2">
                  <InputOTPSlot
                    index={0}
                    className="w-12 h-12 text-lg font-mono text-white bg-slate-800/50 border-slate-600/50 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/30 hover:border-slate-500 transition-all duration-200 rounded-xl first:border-l"
                  />
                  <InputOTPSlot
                    index={1}
                    className="w-12 h-12 text-lg font-mono text-white bg-slate-800/50 border-slate-600/50 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/30 hover:border-slate-500 transition-all duration-200 rounded-xl"
                  />
                  <InputOTPSlot
                    index={2}
                    className="w-12 h-12 text-lg font-mono text-white bg-slate-800/50 border-slate-600/50 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/30 hover:border-slate-500 transition-all duration-200 rounded-xl"
                  />
                </InputOTPGroup>
                <InputOTPGroup className="gap-2">
                  <InputOTPSlot
                    index={3}
                    className="w-12 h-12 text-lg font-mono text-white bg-slate-800/50 border-slate-600/50 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/30 hover:border-slate-500 transition-all duration-200 rounded-xl"
                  />
                  <InputOTPSlot
                    index={4}
                    className="w-12 h-12 text-lg font-mono text-white bg-slate-800/50 border-slate-600/50 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/30 hover:border-slate-500 transition-all duration-200 rounded-xl"
                  />
                  <InputOTPSlot
                    index={5}
                    className="w-12 h-12 text-lg font-mono text-white bg-slate-800/50 border-slate-600/50 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/30 hover:border-slate-500 transition-all duration-200 rounded-xl last:border-r"
                  />
                </InputOTPGroup>
              </InputOTP>
              <p className="text-xs text-slate-500 text-center">
                Enter the 6-digit code from your email
              </p>
            </div>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={csrfLoading || !csrfToken || code.length !== 6}
            className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-semibold py-4 rounded-xl shadow-lg hover:shadow-xl hover:shadow-purple-500/25 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.01] active:scale-[0.99] mt-6 disabled:hover:scale-100"
          >
            {csrfLoading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                Loading...
              </div>
            ) : (
              'Verify Email'
            )}
          </button>
        </form>

        {/* Help Text */}
        <div className="mt-8 p-4 bg-slate-800/30 rounded-xl border border-slate-700/50">
          <p className="text-sm text-slate-400 text-center">
            Didn't receive a code?{' '}
            <Link
              href="/signup"
              className="text-purple-400 hover:text-purple-300 font-semibold hover:underline transition-colors"
            >
              Try signing up again
            </Link>
          </p>
        </div>

        {/* Back to Sign In */}
        <p className="text-center text-sm text-slate-400 mt-6">
          Remember your password?{' '}
          <Link
            href="/signin"
            className="text-purple-400 hover:text-purple-300 font-semibold hover:underline transition-colors"
          >
            Sign in instead
          </Link>
        </p>
        <p className="text-center text-sm text-slate-400 mt-10">
          &copy; 2025 qBraid Co. All rights reserved.
        </p>
      </div>
    </div>
  );
}
