'use client';

import { useState, useCallback } from 'react';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { Mail, Send, AlertCircle, ArrowLeft, CheckCircle } from 'lucide-react';
import Link from 'next/link';
import { initiatePasswordReset } from '@/app/(auth)/actions';
import { useCSRFToken, CSRFTokenInput } from '@/lib/csrf';

interface FormState {
  email: string;
}

const INITIAL_FORM_STATE: FormState = {
  email: '',
};

export function ForgotPasswordForm() {
  const { csrfToken, loading: csrfLoading, error: csrfError } = useCSRFToken();
  const router = useRouter();
  const [formState, setFormState] = useState<FormState>(INITIAL_FORM_STATE);

  // React Query mutation for password reset
  const resetMutation = useMutation({
    mutationFn: async (formData: { email: string; csrfToken: string }) => {
      const formDataObj = new FormData();
      formDataObj.set('email', formData.email);
      formDataObj.set('csrfToken', formData.csrfToken);

      const result = await initiatePasswordReset(null, formDataObj);
      if (!result.success) {
        throw new Error(result.error || 'Failed to send reset code');
      }
      return result;
    },
    onSuccess: (data) => {
      if (data.nextStep === 'reset') {
        // Store email securely in sessionStorage for the reset step
        sessionStorage.setItem('reset-email', formState.email);
        // Navigate to reset password page without email in URL
        router.push('/reset-password');
      }
    },
  });

  // Update form state helper
  const updateFormState = useCallback((updates: Partial<FormState>) => {
    setFormState((prev) => ({ ...prev, ...updates }));
  }, []);

  // Form validation
  const isEmailValid =
    formState.email.includes('@') && formState.email.includes('.') && formState.email.length > 5;
  const canSubmit = isEmailValid && !resetMutation.isPending;

  // Handle form submission
  const handleSubmit = useCallback(
    (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      if (!canSubmit || !csrfToken) return;

      resetMutation.mutate({
        email: formState.email,
        csrfToken,
      });
    },
    [canSubmit, csrfToken, formState.email, resetMutation],
  );

  const displayError = resetMutation.error?.message || csrfError;
  const isSuccess = resetMutation.isSuccess;

  return (
    <div className="w-full bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] rounded-3xl shadow-2xl border border-purple-800/20 backdrop-blur-sm">
      <div className="p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 mb-6 mx-auto shadow-lg shadow-purple-500/25">
            <Send className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">Forgot Password</h1>
          <p className="text-slate-400 text-sm">
            Enter your email address and we'll send you a password reset code
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-5">
          <CSRFTokenInput csrfToken={csrfToken} />

          {/* Error Alert */}
          {displayError && (
            <div className="flex items-start gap-3 p-4 rounded-xl bg-red-500/10 border border-red-500/20 backdrop-blur-sm">
              <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
              <p className="text-red-400 text-sm">{displayError}</p>
            </div>
          )}

          {/* Success message */}
          {isSuccess && (
            <div className="flex items-start gap-3 p-4 rounded-xl bg-green-500/10 border border-green-500/20 backdrop-blur-sm">
              <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <p className="text-green-400 text-sm font-medium">Reset code sent!</p>
                <p className="text-green-300 text-xs mt-1">
                  Redirecting you to enter the reset code...
                </p>
              </div>
            </div>
          )}

          {/* Email Address */}
          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium text-slate-300 block">
              Email Address
            </label>
            <div className="relative group">
              <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-slate-500 group-focus-within:text-purple-400 transition-colors" />
              <input
                id="email"
                name="email"
                type="email"
                placeholder="Enter your email address"
                autoComplete="email"
                value={formState.email}
                onChange={(e) => updateFormState({ email: e.target.value })}
                required
                disabled={resetMutation.isPending || isSuccess}
                className="w-full pl-10 pr-4 py-3.5 rounded-xl border border-slate-600/50 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500 bg-slate-800/50 text-white placeholder:text-slate-500 transition-all duration-200 hover:border-slate-500 disabled:opacity-50 disabled:cursor-not-allowed"
                aria-describedby={!isEmailValid && formState.email ? 'email-error' : undefined}
              />
            </div>

            {/* Email validation feedback */}
            {formState.email && !isEmailValid && (
              <p id="email-error" className="text-xs text-red-400 flex items-center gap-1">
                <AlertCircle className="w-3 h-3" />
                Please enter a valid email address
              </p>
            )}

            {formState.email && isEmailValid && (
              <p className="text-xs text-green-400 flex items-center gap-1">
                <CheckCircle className="w-3 h-3" />
                Email format looks good
              </p>
            )}
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={csrfLoading || !csrfToken || !canSubmit || isSuccess}
            className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-semibold py-4 rounded-xl shadow-lg hover:shadow-xl hover:shadow-purple-500/25 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.01] active:scale-[0.99] mt-6 disabled:hover:scale-100"
            aria-label="Send password reset code"
          >
            {csrfLoading || resetMutation.isPending ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                {csrfLoading ? 'Loading...' : 'Sending...'}
              </div>
            ) : isSuccess ? (
              <div className="flex items-center justify-center gap-2">
                <CheckCircle className="w-4 h-4" aria-hidden="true" />
                Code Sent!
              </div>
            ) : (
              <>
                <Send className="w-4 h-4 mr-2 inline" aria-hidden="true" />
                Send Reset Code
              </>
            )}
          </button>
        </form>

        {/* Help Text */}
        <div className="mt-8 p-4 bg-slate-800/30 rounded-xl border border-slate-700/50">
          <div className="text-sm text-slate-400 text-center space-y-2">
            <p>
              We'll send a 6-digit verification code to your email address if an account exists.
            </p>
            <p className="text-xs text-slate-500">
              The code will expire in 15 minutes for security reasons.
            </p>
          </div>
        </div>

        {/* Navigation Links */}
        <div className="mt-6 space-y-4">
          {/* Back to Sign In */}
          <div className="flex items-center justify-center gap-2 text-center text-sm text-slate-400">
            <ArrowLeft className="w-4 h-4" aria-hidden="true" />
            <span>Back to</span>
            <Link
              href="/signin"
              className="text-purple-400 hover:text-purple-300 font-medium underline underline-offset-2 transition-colors duration-200"
            >
              Sign In
            </Link>
          </div>

          {/* Create Account */}
          <div className="text-center text-sm text-slate-500">
            Don't have an account?{' '}
            <Link
              href="/signup"
              className="text-purple-400 hover:text-purple-300 font-medium underline underline-offset-2 transition-colors duration-200"
            >
              Create one here
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
