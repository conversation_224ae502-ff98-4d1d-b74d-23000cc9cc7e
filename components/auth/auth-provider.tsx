'use client';

import { createContext, useContext, ReactNode, useRef, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { configureAmplify } from '@/lib/amplify-config';
import { AuthResult } from '@/types/auth';

interface AuthContextType {
  isAmplifyConfigured: boolean;
  handleAuthSuccess: (result: AuthResult, redirectPath?: string) => void;
}

const AuthContext = createContext<AuthContextType | null>(null);

export function AuthProvider({ children }: { children: ReactNode }) {
  const router = useRouter();
  const isConfiguredRef = useRef(false);
  const [isAmplifyConfigured, setIsAmplifyConfigured] = useState(false);

  // Configure Amplify after hydration to avoid SSR mismatch
  useEffect(() => {
    if (!isConfiguredRef.current) {
      configureAmplify();
      isConfiguredRef.current = true;
      setIsAmplifyConfigured(true);
    }
  }, []);

  const handleAuthSuccess = (result: AuthResult, redirectPath?: string) => {
    if (result.success) {
      const finalRedirect = result.redirectTo || redirectPath || '/';
      router.push(finalRedirect);
      router.refresh();
    }
  };

  return (
    <AuthContext.Provider
      value={{
        isAmplifyConfigured,
        handleAuthSuccess,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
}
