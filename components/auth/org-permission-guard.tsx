'use client';

import React from 'react';
import { Permission } from '@/types/auth';
import { useOrgPermissions } from '@/hooks/use-permissions';
import { useOrgContext } from '@/components/org/org-context-provider';

interface OrgPermissionGuardProps {
  /** Required permission(s) to access the content */
  permission?: Permission | Permission[];
  /** Required role(s) to access the content */
  role?: string | string[];
  /** Organization ID to check permissions for. If not provided, uses current org */
  orgId?: string;
  /** Whether to require ALL permissions/roles or just ANY */
  requireAll?: boolean;
  /** Fallback content when permission is denied */
  fallback?: React.ReactNode;
  /** Loading content while checking permissions */
  loading?: React.ReactNode;
  /** Children to render when permission is granted */
  children: React.ReactNode;
  /** Whether to show debug information */
  debug?: boolean;
}

/**
 * Organization-aware permission guard component
 * Automatically checks permissions within the current organization context
 * 
 * @example
 * ```tsx
 * // Check permission in current organization
 * <OrgPermissionGuard permission={Permission.ManageDevices}>
 *   <DeviceManagementPanel />
 * </OrgPermissionGuard>
 * 
 * // Check permission in specific organization
 * <OrgPermissionGuard permission={Permission.ViewEarnings} orgId="specific-org-id">
 *   <EarningsReport />
 * </OrgPermissionGuard>
 * 
 * // Check multiple permissions (require all)
 * <OrgPermissionGuard 
 *   permission={[Permission.ViewTeam, Permission.ManageTeam]} 
 *   requireAll={true}
 * >
 *   <TeamManagementPanel />
 * </OrgPermissionGuard>
 * 
 * // Check role-based access
 * <OrgPermissionGuard role={['admin', 'owner']}>
 *   <AdminPanel />
 * </OrgPermissionGuard>
 * ```
 */
export function OrgPermissionGuard({
  permission,
  role,
  orgId,
  requireAll = false,
  fallback = null,
  loading = <div>Checking permissions...</div>,
  children,
  debug = false,
}: OrgPermissionGuardProps) {
  const { currentOrgId } = useOrgContext();
  const effectiveOrgId = orgId || currentOrgId;
  
  const {
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    isLoading,
    hasError,
    error,
    currentOrgRole,
    isOrgSpecific,
    orgContext,
  } = useOrgPermissions(effectiveOrgId);

  // Debug logging
  if (debug) {
    console.log(`🛡️ [ORG-PERMISSION-GUARD] Guard evaluation:`, {
      requiredPermission: permission,
      requiredRole: role,
      effectiveOrgId,
      currentOrgRole,
      isOrgSpecific,
      requireAll,
      isLoading,
      hasError,
      orgContext,
    });
  }

  // Show loading state
  if (isLoading) {
    return <>{loading}</>;
  }

  // Show error state (optional - you might want to handle this differently)
  if (hasError) {
    console.error(`❌ [ORG-PERMISSION-GUARD] Permission check failed:`, error);
    return <>{fallback}</>;
  }

  // Check permissions
  let hasRequiredPermissions = true;
  if (permission) {
    const permissions = Array.isArray(permission) ? permission : [permission];
    
    if (requireAll) {
      hasRequiredPermissions = permissions.every(p => hasPermission(p));
    } else {
      hasRequiredPermissions = permissions.some(p => hasPermission(p));
    }
  }

  // Check roles
  let hasRequiredRoles = true;
  if (role) {
    const roles = Array.isArray(role) ? role : [role];
    
    if (requireAll) {
      hasRequiredRoles = hasAllRoles(roles);
    } else {
      hasRequiredRoles = hasAnyRole(roles);
    }
  }

  // Final access decision
  const hasAccess = hasRequiredPermissions && hasRequiredRoles;

  if (debug) {
    console.log(`🛡️ [ORG-PERMISSION-GUARD] Access decision:`, {
      hasRequiredPermissions,
      hasRequiredRoles,
      hasAccess,
      effectiveOrgId,
      currentOrgRole,
    });
  }

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Higher-order component version of OrgPermissionGuard
 * Useful for wrapping entire components or pages
 */
export function withOrgPermissions<P extends object>(
  Component: React.ComponentType<P>,
  guardProps: Omit<OrgPermissionGuardProps, 'children'>
) {
  return function WrappedComponent(props: P) {
    return (
      <OrgPermissionGuard {...guardProps}>
        <Component {...props} />
      </OrgPermissionGuard>
    );
  };
}

/**
 * Hook for imperative permission checking within organization context
 * Useful for conditional logic within components
 */
export function useOrgPermissionCheck(orgId?: string) {
  const {
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    currentOrgRole,
    isOrgSpecific,
    isLoading,
    hasError,
  } = useOrgPermissions(orgId);

  const checkPermission = (permission: Permission | Permission[], requireAll = false): boolean => {
    if (isLoading || hasError) return false;
    
    const permissions = Array.isArray(permission) ? permission : [permission];
    
    if (requireAll) {
      return permissions.every(p => hasPermission(p));
    } else {
      return permissions.some(p => hasPermission(p));
    }
  };

  const checkRole = (role: string | string[], requireAll = false): boolean => {
    if (isLoading || hasError) return false;
    
    const roles = Array.isArray(role) ? role : [role];
    
    if (requireAll) {
      return hasAllRoles(roles);
    } else {
      return hasAnyRole(roles);
    }
  };

  const checkAccess = (
    permission?: Permission | Permission[],
    role?: string | string[],
    requireAll = false
  ): boolean => {
    if (isLoading || hasError) return false;
    
    let hasPermissions = true;
    let hasRoles = true;
    
    if (permission) {
      hasPermissions = checkPermission(permission, requireAll);
    }
    
    if (role) {
      hasRoles = checkRole(role, requireAll);
    }
    
    return hasPermissions && hasRoles;
  };

  return {
    checkPermission,
    checkRole,
    checkAccess,
    currentOrgRole,
    isOrgSpecific,
    isLoading,
    hasError,
  };
}

/**
 * Utility function to check if user has admin access in current organization
 */
export function useIsOrgAdmin(orgId?: string): boolean {
  const { hasRole, isLoading, hasError } = useOrgPermissions(orgId);
  
  if (isLoading || hasError) return false;
  
  return hasRole('admin') || hasRole('owner');
}

/**
 * Utility function to check if user can manage the current organization
 */
export function useCanManageOrg(orgId?: string): boolean {
  const { hasPermission, isLoading, hasError } = useOrgPermissions(orgId);
  
  if (isLoading || hasError) return false;
  
  return hasPermission(Permission.ManageTeam) || hasPermission(Permission.AdminAccess);
}
