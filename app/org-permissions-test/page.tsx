'use client';

import React from 'react';
import { OrgPermissionsDemo } from '@/components/demo/org-permissions-demo';
import { OrgContextProvider } from '@/components/org/org-context-provider';

/**
 * Test page for organization-specific permissions
 * This page demonstrates the enhanced role caching system with organization awareness
 */
export default function OrgPermissionsTestPage() {
  return (
    <OrgContextProvider>
      <div className="min-h-screen bg-gray-100">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Organization-Specific Permissions Test
            </h1>
            <p className="text-gray-600 max-w-3xl">
              This page demonstrates the enhanced role caching system with organization-aware permissions. 
              Switch between organizations to see how your permissions change based on your role in each organization.
            </p>
          </div>

          {/* Key Features */}
          <div className="mb-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              🚀 Key Features Demonstrated
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h3 className="font-semibold text-blue-900 mb-2">Organization Context</h3>
                <p className="text-blue-700 text-sm">
                  Automatic organization switching with persistent localStorage state
                </p>
              </div>
              <div className="p-4 bg-green-50 rounded-lg">
                <h3 className="font-semibold text-green-900 mb-2">Dynamic Permissions</h3>
                <p className="text-green-700 text-sm">
                  Role-specific permissions that change based on current organization
                </p>
              </div>
              <div className="p-4 bg-purple-50 rounded-lg">
                <h3 className="font-semibold text-purple-900 mb-2">Permission Guards</h3>
                <p className="text-purple-700 text-sm">
                  Declarative components for protecting UI based on permissions
                </p>
              </div>
              <div className="p-4 bg-yellow-50 rounded-lg">
                <h3 className="font-semibold text-yellow-900 mb-2">Caching System</h3>
                <p className="text-yellow-700 text-sm">
                  Enhanced [emailId, orgId, role] caching structure with Redis
                </p>
              </div>
              <div className="p-4 bg-red-50 rounded-lg">
                <h3 className="font-semibold text-red-900 mb-2">Real-time Updates</h3>
                <p className="text-red-700 text-sm">
                  TanStack Query integration with automatic cache invalidation
                </p>
              </div>
              <div className="p-4 bg-indigo-50 rounded-lg">
                <h3 className="font-semibold text-indigo-900 mb-2">Type Safety</h3>
                <p className="text-indigo-700 text-sm">
                  Full TypeScript support with proper permission and role types
                </p>
              </div>
            </div>
          </div>

          {/* API Endpoints */}
          <div className="mb-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              🔗 API Endpoints
            </h2>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-mono rounded">
                  GET
                </span>
                <code className="text-sm font-mono text-gray-700">
                  /api/auth/permissions
                </code>
                <span className="text-gray-500 text-sm">Global permissions (all organizations)</span>
              </div>
              <div className="flex items-center space-x-3">
                <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-mono rounded">
                  GET
                </span>
                <code className="text-sm font-mono text-gray-700">
                  /api/auth/permissions?orgId=68659d197705c09bd8526521
                </code>
                <span className="text-gray-500 text-sm">Organization-specific permissions</span>
              </div>
            </div>
          </div>

          {/* Demo Component */}
          <OrgPermissionsDemo />

          {/* Technical Details */}
          <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              🔧 Technical Implementation
            </h2>
            <div className="prose prose-sm max-w-none">
              <h3 className="text-lg font-semibold text-gray-800">Architecture Overview</h3>
              <ul className="text-gray-600 space-y-1">
                <li><strong>Backend:</strong> Enhanced API with organization-aware permission fetching</li>
                <li><strong>Caching:</strong> Redis-based role caching with [emailId, orgId, role] structure</li>
                <li><strong>Frontend:</strong> TanStack Query for reactive data management</li>
                <li><strong>Context:</strong> Organization context provider for state management</li>
                <li><strong>Guards:</strong> Permission guard components for declarative access control</li>
              </ul>

              <h3 className="text-lg font-semibold text-gray-800 mt-6">Permission Flow</h3>
              <ol className="text-gray-600 space-y-1">
                <li>1. User selects organization (stored in localStorage)</li>
                <li>2. Organization context provider updates current org state</li>
                <li>3. Permission hooks automatically fetch org-specific permissions</li>
                <li>4. API returns role and permissions for the specific organization</li>
                <li>5. UI components react to permission changes automatically</li>
                <li>6. Cache invalidation ensures fresh data on organization switch</li>
              </ol>

              <h3 className="text-lg font-semibold text-gray-800 mt-6">Key Benefits</h3>
              <ul className="text-gray-600 space-y-1">
                <li><strong>Performance:</strong> Granular caching prevents unnecessary API calls</li>
                <li><strong>Scalability:</strong> Supports multi-user, multi-organization scenarios</li>
                <li><strong>Security:</strong> Organization-scoped permissions prevent privilege escalation</li>
                <li><strong>Developer Experience:</strong> Clean APIs and declarative permission checking</li>
                <li><strong>Maintainability:</strong> Type-safe implementation with clear abstractions</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </OrgContextProvider>
  );
}
