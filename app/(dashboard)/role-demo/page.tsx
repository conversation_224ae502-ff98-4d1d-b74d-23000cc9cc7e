'use client';

import React from 'react';
import { OrgRoleGuard, useOrgRole } from '@/components/auth/org-role-guard';
import { RoleLoader, PermissionWrapper } from '@/components/auth/role-loader';
import { OrgInfo, OrgSelector } from '@/components/org/org-context-provider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, Shield, Eye, Settings, Users, Crown } from 'lucide-react';
import { Permission } from '@/types/auth';

/**
 * Role Demo Page - Shows role checking and loading states in action
 * This page demonstrates how the @role-loader.tsx component works
 * and how role checking is visible throughout the application
 */
export default function RoleDemoPage() {
  const { role, organization, loading, isAdmin, canManage } = useOrgRole();

  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight mb-2">Role Loading & Checking Demo</h1>
        <p className="text-lg text-muted-foreground mb-4">
          See how role checking and loading states work throughout the application
        </p>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h2 className="font-semibold text-blue-800 mb-2">🔍 Role Loader Visibility</h2>
          <p className="text-blue-700 text-sm">
            The RoleLoader component is now visible and provides better UX with loading states, 
            organization context, and role-based access control. Switch organizations to see 
            how roles change dynamically.
          </p>
        </div>
      </div>

      {/* Current State Display */}
      <div className="grid md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Current Role State
            </CardTitle>
            <CardDescription>Your current organization and role</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <OrgInfo showRole showCount className="text-lg" />
              <Badge variant={isAdmin() ? 'default' : 'secondary'}>
                {role || 'No Role'}
              </Badge>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Loading:</span>
                <Badge variant={loading ? 'destructive' : 'outline'}>
                  {loading ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div className="flex justify-between text-sm">
                <span>Can Manage:</span>
                <Badge variant={canManage() ? 'default' : 'outline'}>
                  {canManage() ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div className="flex justify-between text-sm">
                <span>Is Admin:</span>
                <Badge variant={isAdmin() ? 'default' : 'outline'}>
                  {isAdmin() ? 'Yes' : 'No'}
                </Badge>
              </div>
            </div>

            <OrgSelector className="w-full" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Loader2 className="h-5 w-5" />
              Loading States Demo
            </CardTitle>
            <CardDescription>See how loading states appear</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="font-medium text-sm mb-2">Simulated Loading State:</div>
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                  <span className="text-sm text-gray-600">Checking permissions...</span>
                </div>
              </div>
              
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="font-medium text-sm text-green-800 mb-1">✅ Loaded State</div>
                <div className="text-xs text-green-600">
                  Role data cached and ready for instant navigation
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Role-Based Content Examples */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Role-Based Content Examples</h2>
        
        <div className="grid md:grid-cols-3 gap-6">
          {/* Admin Only */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Crown className="h-5 w-5 text-yellow-500" />
                Admin Only
              </CardTitle>
            </CardHeader>
            <CardContent>
              <OrgRoleGuard 
                requiredRoles={['admin', 'owner']} 
                showRoleInfo
                fallback={
                  <div className="text-center py-6 text-gray-500">
                    <Crown className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Admin access required</p>
                  </div>
                }
              >
                <div className="space-y-3">
                  <Button className="w-full" variant="default">
                    <Settings className="h-4 w-4 mr-2" />
                    Admin Settings
                  </Button>
                  <Button className="w-full" variant="outline">
                    <Users className="h-4 w-4 mr-2" />
                    Manage Users
                  </Button>
                </div>
              </OrgRoleGuard>
            </CardContent>
          </Card>

          {/* Member+ */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Users className="h-5 w-5 text-blue-500" />
                Member+
              </CardTitle>
            </CardHeader>
            <CardContent>
              <OrgRoleGuard 
                requiredRoles={['member', 'admin', 'owner']} 
                showRoleInfo
                fallback={
                  <div className="text-center py-6 text-gray-500">
                    <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Member access required</p>
                  </div>
                }
              >
                <div className="space-y-3">
                  <Button className="w-full" variant="default">
                    View Resources
                  </Button>
                  <Button className="w-full" variant="outline">
                    Submit Reports
                  </Button>
                </div>
              </OrgRoleGuard>
            </CardContent>
          </Card>

          {/* Viewer+ */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Eye className="h-5 w-5 text-gray-500" />
                Viewer+
              </CardTitle>
            </CardHeader>
            <CardContent>
              <OrgRoleGuard 
                requiredRoles={['viewer', 'member', 'admin', 'owner']} 
                showRoleInfo
                fallback={
                  <div className="text-center py-6 text-gray-500">
                    <Eye className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Organization access required</p>
                  </div>
                }
              >
                <div className="space-y-3">
                  <Button className="w-full" variant="default">
                    View Dashboard
                  </Button>
                  <Button className="w-full" variant="outline">
                    Read Documentation
                  </Button>
                </div>
              </OrgRoleGuard>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Legacy Permission Wrapper Example */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Legacy Permission Wrapper (for comparison)</CardTitle>
          <CardDescription>
            The old permission system for comparison - still works but less user-friendly
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-4">
            <PermissionWrapper 
              requiredPermissions={[Permission.ManageDevices]}
              fallback={<div className="text-gray-500 p-4 border rounded">No device management permission</div>}
            >
              <div className="p-4 bg-green-50 border border-green-200 rounded">
                ✅ Device management access granted
              </div>
            </PermissionWrapper>

            <PermissionWrapper 
              requiredPermissions={[Permission.AdminAccess]}
              fallback={<div className="text-gray-500 p-4 border rounded">No admin permission</div>}
            >
              <div className="p-4 bg-blue-50 border border-blue-200 rounded">
                ✅ Admin access granted
              </div>
            </PermissionWrapper>
          </div>
        </CardContent>
      </Card>

      {/* Performance Info */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>🚀 Improvements Made</CardTitle>
          <CardDescription>
            What's better about the new role checking system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-semibold text-green-800">✅ New System Benefits</h4>
              <ul className="text-sm space-y-1 text-green-700">
                <li>• Organization-aware role checking</li>
                <li>• Persistent organization selection</li>
                <li>• Better loading states and UX</li>
                <li>• Cleaner component API</li>
                <li>• Reactive organization switching</li>
                <li>• Role information display</li>
              </ul>
            </div>
            <div className="space-y-3">
              <h4 className="font-semibold text-yellow-800">⚠️ Previous Issues</h4>
              <ul className="text-sm space-y-1 text-yellow-700">
                <li>• Complex permission mapping</li>
                <li>• No organization context</li>
                <li>• Poor loading states</li>
                <li>• Multiple role checking systems</li>
                <li>• No persistent org selection</li>
                <li>• Confusing for users</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
