'use client';

import { useState, useMemo } from 'react';
import type { DeviceCardProps } from '@/types/device';
import { useAllDevices } from '@/hooks/use-api';
import { Input } from '@/components/ui/input';
import { Search, Loader2, Filter, ChevronDown, PlusIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu';
import { DeviceOverviewCard } from '@/components/device-overview-card';
import { DeviceStatsCards } from '@/components/device-stats-cards';
import Link from 'next/link';

export default function DevicesPage() {
  const { data: devices, isLoading } = useAllDevices();
  const [search, setSearch] = useState('');
  const [providerFilter, setProviderFilter] = useState<string>('All Providers');

  const providers = useMemo<string[]>(() => {
    return Array.from(new Set((devices || []).map((d: DeviceCardProps) => d.provider)));
  }, [devices]);

  const filteredDevices = useMemo(
    () =>
      (devices || []).filter((device: DeviceCardProps) => {
        const matchesName = device.name?.toLowerCase().includes(search.toLowerCase());
        const matchesProvider =
          providerFilter === 'All Providers' ||
          device.provider.toLowerCase() === providerFilter.toLowerCase();
        return matchesName && matchesProvider;
      }),
    [devices, search, providerFilter],
  );

  return (
    <div className="min-h-screen">
      {/* Navbar provided by root layout */}

      <main className="max-w-[1400px] mx-auto px-6 ">
        {/* Header Section */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
          <h1 className="text-3xl font-semibold text-white">Devices</h1>
          <Button variant="default" className="bg-[#8a2be2] hover:bg-[#7c2dd5] text-white" asChild>
            <Link href="/add-device" className="flex items-center gap-2">
              <PlusIcon className="size-4" /> Add Device
            </Link>
          </Button>
        </div>

        {/* Stats cards */}
        <DeviceStatsCards
          total={(devices || []).length}
          online={
            (devices || []).filter(
              (d) => d.status?.toLowerCase() === 'online' || d.status?.toLowerCase() === 'active',
            ).length
          }
          offline={(devices || []).filter((d) => d.status?.toLowerCase() === 'offline').length}
          servers={(devices || []).filter((d) => d.type?.toLowerCase().includes('server')).length}
        />

        {/* Search & Filter Section */}
        <div className="mb-8 flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-[#6b7280] w-5 h-5 transition-colors duration-200" />
            <Input
              placeholder="Search devices by name..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-12 pr-4 py-3 bg-sidebar border-sidebar-border text-white placeholder:text-[#6b7280] text-base rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200"
            />
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                aria-label="Filter by provider"
                variant="outline"
                className="bg-sidebar border-sidebar-border text-[#6b7280] hover:bg-sidebar/60 hover:text-white"
              >
                <Filter className="w-5 h-5 mr-2" />
                {providerFilter}
                <ChevronDown className="w-4 h-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-sidebar border-sidebar-border">
              <DropdownMenuItem
                className="text-[#6b7280] hover:text-white hover:bg-[#374151]"
                onClick={() => setProviderFilter('All Providers')}
              >
                All Providers
              </DropdownMenuItem>
              {providers.map((prov) => (
                <DropdownMenuItem
                  key={prov}
                  className="text-[#6b7280] hover:text-white hover:bg-[#374151]"
                  onClick={() => setProviderFilter(prov)}
                >
                  {prov}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Loading / Empty / Grid State */}
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-20">
            <Loader2 className="w-8 h-8 text-[#8a2be2] animate-spin mb-4" />
            <div className="text-[#6b7280] text-lg mb-2">Loading devices...</div>
            <p className="text-[#9ca3af] text-base">
              Please wait while we fetch your quantum devices
            </p>
          </div>
        ) : filteredDevices.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-20">
            <div className="text-[#6b7280] text-xl mb-2">No devices found</div>
            <p className="text-[#9ca3af] text-base">Try adjusting your search criteria</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredDevices.map((device) => (
              <DeviceOverviewCard key={device.qbraid_id} {...device} />
            ))}
          </div>
        )}
      </main>
    </div>
  );
}
