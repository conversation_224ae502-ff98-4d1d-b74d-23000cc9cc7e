'use client';

import React from 'react';
import { Inter } from 'next/font/google';
import { PermissionProvider } from '@/components/rbac/permission-provider';
import { RoleLoader } from '@/components/auth/role-loader';
import { OrgContextProvider } from '@/components/org/org-context-provider';
import { AppSidebar } from '@/components/app-sidebar';
import { SiteHeader } from '@/components/site-header';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  return (
    <PermissionProvider>
      <OrgContextProvider>
        <SidebarProvider>
          {/* Collapsible sidebar on the left */}
          <AppSidebar variant="inset" />

          {/* Main frame: header + routed page content */}
          <SidebarInset className="flex min-h-screen flex-col bg-[#0f0e14]">
            <SiteHeader />
            <RoleLoader>
              <div className="flex-1 p-4 md:p-6 lg:p-8">{children}</div>
            </RoleLoader>
          </SidebarInset>
        </SidebarProvider>
      </OrgContextProvider>
    </PermissionProvider>
  );
}
