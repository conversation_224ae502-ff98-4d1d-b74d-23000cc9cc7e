'use client';

import { Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { EditDeviceForm } from '@/components/edit-device-form';
import { useDeviceData } from '@/hooks/use-api';

function EditDeviceContent() {
  const searchParams = useSearchParams();
  const deviceId = searchParams.get('id');

  // Fetch device data using the React Query API hook
  const { data: deviceData, isLoading } = useDeviceData(deviceId || '');

  if (isLoading || !deviceData) {
    return <div className="text-white p-8">Loading...</div>;
  }

  return <EditDeviceForm deviceData={deviceData} isEdit={true} />;
}

export default function EditDevicePage() {
  return (
    <Suspense fallback={<div className="text-white p-8">Loading...</div>}>
      <EditDeviceContent />
    </Suspense>
  );
}
