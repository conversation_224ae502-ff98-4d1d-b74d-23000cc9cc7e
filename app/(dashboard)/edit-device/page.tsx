'use client';

import { Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { EditDeviceForm } from '@/components/edit-device-form';
import { useDeviceData } from '@/hooks/use-api';
import { OrgPermissionGuard } from '@/components/auth/org-permission-guard';
import { Permission } from '@/types/auth';
import { AlertCircle, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

function EditDeviceContent() {
  const searchParams = useSearchParams();
  const deviceId = searchParams.get('id');

  // Fetch device data using the React Query API hook
  const { data: deviceData, isLoading } = useDeviceData(deviceId || '');

  if (isLoading || !deviceData) {
    return <div className="text-white p-8">Loading...</div>;
  }

  return <EditDeviceForm deviceData={deviceData} isEdit={true} />;
}

export default function EditDevicePage() {
  return (
    <OrgPermissionGuard
      permission={Permission.ManageDevices}
      fallback={
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center p-8 bg-white border border-red-200 rounded-lg shadow-sm max-w-md">
            <div className="text-red-500 mb-4">
              <AlertCircle className="w-12 h-12 mx-auto" />
            </div>
            <h2 className="text-xl font-semibold text-red-800 mb-2">Access Denied</h2>
            <p className="text-red-600 mb-6">
              You need device management permissions to edit devices.
            </p>
            <div className="space-y-3">
              <Link
                href="/devices"
                className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Devices
              </Link>
              <div className="text-sm text-gray-500">
                Contact your administrator to request device management access.
              </div>
            </div>
          </div>
        </div>
      }
    >
      <Suspense fallback={<div className="text-white p-8">Loading...</div>}>
        <EditDeviceContent />
      </Suspense>
    </OrgPermissionGuard>
  );
}
