'use client';

import { useState } from 'react';
import { OrgPermissionGuard } from '@/components/auth/org-permission-guard';
import { Permission } from '@/types/auth';
import { useOrgPermissions } from '@/hooks/use-permissions';
import { useOrgContext } from '@/components/org/org-context-provider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  User, 
  Mail, 
  Building2, 
  Shield, 
  Settings, 
  AlertCircle,
  CheckCircle,
  Edit3,
  Save,
  X
} from 'lucide-react';
import Link from 'next/link';

export default function ProfilePage() {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: '<PERSON>',
    email: '<EMAIL>',
    title: 'Senior Quantum Engineer',
    department: 'Research & Development'
  });

  const { 
    roles, 
    permissions, 
    currentOrgRole, 
    isOrgSpecific,
    orgRoles,
    currentOrgId 
  } = useOrgPermissions();
  
  const { organizations } = useOrgContext();

  const handleSave = () => {
    // TODO: Implement profile update API call
    setIsEditing(false);
  };

  const handleCancel = () => {
    // Reset form data
    setFormData({
      name: 'Alex Johnson',
      email: '<EMAIL>',
      title: 'Senior Quantum Engineer',
      department: 'Research & Development'
    });
    setIsEditing(false);
  };

  return (
    <OrgPermissionGuard
      permission={Permission.ViewProfile}
      fallback={
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center p-8 bg-white border border-red-200 rounded-lg shadow-sm max-w-md">
            <div className="text-red-500 mb-4">
              <AlertCircle className="w-12 h-12 mx-auto" />
            </div>
            <h2 className="text-xl font-semibold text-red-800 mb-2">Access Denied</h2>
            <p className="text-red-600 mb-6">You need profile view permissions to access this page.</p>
            <Link 
              href="/" 
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Return to Dashboard
            </Link>
          </div>
        </div>
      }
    >
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Profile Settings</h1>
            <p className="text-gray-600 mt-2">Manage your account information and organization access.</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Profile Information */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <User className="h-5 w-5" />
                      Personal Information
                    </CardTitle>
                    <CardDescription>
                      Update your personal details and contact information.
                    </CardDescription>
                  </div>
                  <OrgPermissionGuard
                    permission={Permission.EditProfile}
                    fallback={
                      <Badge variant="outline" className="text-gray-500">
                        View Only
                      </Badge>
                    }
                  >
                    {!isEditing ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsEditing(true)}
                        className="flex items-center gap-2"
                      >
                        <Edit3 className="h-4 w-4" />
                        Edit
                      </Button>
                    ) : (
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleCancel}
                          className="flex items-center gap-2"
                        >
                          <X className="h-4 w-4" />
                          Cancel
                        </Button>
                        <Button
                          size="sm"
                          onClick={handleSave}
                          className="flex items-center gap-2"
                        >
                          <Save className="h-4 w-4" />
                          Save
                        </Button>
                      </div>
                    )}
                  </OrgPermissionGuard>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Full Name</Label>
                      {isEditing ? (
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                          className="mt-1"
                        />
                      ) : (
                        <p className="mt-1 text-sm text-gray-900">{formData.name}</p>
                      )}
                    </div>
                    <div>
                      <Label htmlFor="email">Email Address</Label>
                      {isEditing ? (
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                          className="mt-1"
                        />
                      ) : (
                        <p className="mt-1 text-sm text-gray-900 flex items-center gap-2">
                          <Mail className="h-4 w-4 text-gray-400" />
                          {formData.email}
                        </p>
                      )}
                    </div>
                    <div>
                      <Label htmlFor="title">Job Title</Label>
                      {isEditing ? (
                        <Input
                          id="title"
                          value={formData.title}
                          onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                          className="mt-1"
                        />
                      ) : (
                        <p className="mt-1 text-sm text-gray-900">{formData.title}</p>
                      )}
                    </div>
                    <div>
                      <Label htmlFor="department">Department</Label>
                      {isEditing ? (
                        <Input
                          id="department"
                          value={formData.department}
                          onChange={(e) => setFormData({ ...formData, department: e.target.value })}
                          className="mt-1"
                        />
                      ) : (
                        <p className="mt-1 text-sm text-gray-900">{formData.department}</p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Organization & Permissions */}
            <div className="space-y-6">
              {/* Current Organization */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    Current Organization
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {currentOrgId ? (
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {organizations.find(org => org.orgId === currentOrgId)?.orgName || 'Unknown'}
                        </p>
                        <p className="text-xs text-gray-500">Organization ID: {currentOrgId.substring(0, 8)}...</p>
                      </div>
                      <div>
                        <Badge variant="secondary" className="flex items-center gap-1 w-fit">
                          <Shield className="h-3 w-3" />
                          {currentOrgRole || 'No role'}
                        </Badge>
                      </div>
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">No organization selected</p>
                  )}
                </CardContent>
              </Card>

              {/* All Organizations */}
              <Card>
                <CardHeader>
                  <CardTitle>All Organizations</CardTitle>
                  <CardDescription>Your access across all organizations</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(orgRoles).map(([orgId, orgRole]) => (
                      <div key={orgId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="text-sm font-medium">{orgRole.orgName}</p>
                          <p className="text-xs text-gray-500">{orgId.substring(0, 8)}...</p>
                        </div>
                        <Badge variant="outline">{orgRole.role}</Badge>
                      </div>
                    ))}
                    {Object.keys(orgRoles).length === 0 && (
                      <p className="text-sm text-gray-500">No organization access</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Current Permissions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Current Permissions
                  </CardTitle>
                  <CardDescription>
                    {isOrgSpecific ? 'Organization-specific permissions' : 'Global permissions'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm font-medium mb-2">Roles ({roles.length})</p>
                      <div className="flex flex-wrap gap-1">
                        {roles.map((role) => (
                          <Badge key={role} variant="secondary" className="text-xs">
                            {role}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <Separator />
                    <div>
                      <p className="text-sm font-medium mb-2">Permissions ({permissions.length})</p>
                      <div className="max-h-32 overflow-y-auto space-y-1">
                        {permissions.map((permission) => (
                          <div key={permission} className="flex items-center gap-2">
                            <CheckCircle className="h-3 w-3 text-green-500" />
                            <span className="text-xs text-gray-600">{permission}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </OrgPermissionGuard>
  );
}
