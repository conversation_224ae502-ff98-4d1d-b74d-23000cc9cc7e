'use client';

import { useOrgPermissions } from '@/hooks/use-permissions';
import { useOrgContext } from '@/components/org/org-context-provider';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, ArrowLeft, Building2 } from 'lucide-react';
import Link from 'next/link';

export default function UnauthorizedPage() {
  const { roles, permissions, currentOrgRole, isOrgSpecific, orgRoles, currentOrgId } =
    useOrgPermissions();

  const { organizations } = useOrgContext();
  const currentOrg = organizations.find((org) => org.orgId === currentOrgId);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl w-full space-y-8">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto h-12 w-12 text-red-400">
              <AlertTriangle className="w-12 h-12" />
            </div>
            <CardTitle className="text-red-600">Access Denied</CardTitle>
            <CardDescription>
              You don't have sufficient permissions to access the requested resource
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Current Organization Context */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2 flex items-center gap-2">
                <Building2 className="h-4 w-4" />
                Current Organization Context
              </h4>
              {currentOrg ? (
                <div className="space-y-2">
                  <p className="text-sm text-blue-800">
                    <span className="font-medium">Organization:</span> {currentOrg.orgName}
                  </p>
                  <p className="text-sm text-blue-800">
                    <span className="font-medium">Your Role:</span> {currentOrgRole || 'No role'}
                  </p>
                  <p className="text-sm text-blue-700">
                    <span className="font-medium">Permission Scope:</span>{' '}
                    {isOrgSpecific ? 'Organization-specific' : 'Global'}
                  </p>
                </div>
              ) : (
                <p className="text-sm text-blue-700">No organization selected</p>
              )}
            </div>

            {/* Current Roles */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">
                Current Roles ({roles.length})
              </h4>
              <div className="flex flex-wrap gap-1">
                {roles.length > 0 ? (
                  roles.map((role) => (
                    <Badge key={role} variant="secondary">
                      {role}
                    </Badge>
                  ))
                ) : (
                  <Badge variant="outline">No roles assigned</Badge>
                )}
              </div>
            </div>

            {/* Current Permissions */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">
                Current Permissions ({permissions.length})
              </h4>
              <div className="max-h-32 overflow-y-auto">
                <div className="flex flex-wrap gap-1">
                  {permissions.length > 0 ? (
                    permissions.map((permission) => (
                      <Badge key={permission} variant="outline" className="text-xs">
                        {permission}
                      </Badge>
                    ))
                  ) : (
                    <Badge variant="outline">No permissions granted</Badge>
                  )}
                </div>
              </div>
            </div>

            {/* All Organizations */}
            {Object.keys(orgRoles).length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">All Organization Access</h4>
                <div className="space-y-2">
                  {Object.entries(orgRoles).map(([orgId, orgRole]) => (
                    <div
                      key={orgId}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded"
                    >
                      <div>
                        <p className="text-sm font-medium">{orgRole.orgName}</p>
                        <p className="text-xs text-gray-500">{orgId.substring(0, 8)}...</p>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {orgRole.role}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="pt-4 border-t">
              <p className="text-sm text-gray-600 mb-4">
                If you believe you should have access to this resource, please contact your
                organization administrator or try switching to a different organization.
              </p>

              <div className="flex flex-col sm:flex-row gap-3">
                <Button asChild className="flex-1">
                  <Link href="/" className="flex items-center gap-2">
                    <ArrowLeft className="h-4 w-4" />
                    Go to Dashboard
                  </Link>
                </Button>
                <Button variant="outline" asChild className="flex-1">
                  <Link href="/profile">View Profile</Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
