import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    if (!body.provider || !body.email || !body.type) {
      return NextResponse.json(
        {
          error: 'Provider, email, and type are required',
        },
        { status: 400 },
      );
    }

    // Proxy to external API
    await externalClient.post('/audit-logs', body);

    return NextResponse.json({
      success: true,
      message: 'Audit log submitted successfully',
    });
  } catch (error: any) {
    console.error('❌ [API Gateway] Error submitting audit log:', error);

    return NextResponse.json(
      {
        error: error.message || 'Failed to submit audit log',
        success: false,
      },
      { status: error.status || 500 },
    );
  }
}
