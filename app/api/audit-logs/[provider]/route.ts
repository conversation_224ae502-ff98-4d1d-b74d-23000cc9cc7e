import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';
import type { ActionLogRowProps } from '@/types/logs';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ provider: string }> },
) {
  try {
    const { provider } = await params;
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || '0';
    const resultsPerPage = searchParams.get('resultsPerPage') || '10';

    if (!provider) {
      return NextResponse.json(
        {
          error: 'Provider is required',
        },
        { status: 400 },
      );
    }

    // Proxy to external API
    const response = await externalClient.get(
      `/audit-logs/${provider}?page=${page}&resultsPerPage=${resultsPerPage}`,
    );

    const data = response.data;

    // Transform timestamps
    data.auditLogsArray = data.auditLogsArray.map((log: ActionLogRowProps) => {
      if (log.createdAt) {
        log.createdAt = new Date(log.createdAt);
      }
      return log;
    });

    return NextResponse.json(data);
  } catch (error: unknown) {
    console.error('❌ [API Gateway] Error fetching audit logs:', error);

    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch audit logs';
    const errorStatus =
      typeof error === 'object' && error !== null && 'status' in error
        ? (error as { status: number }).status
        : 500;

    return NextResponse.json(
      {
        error: errorMessage,
        auditLogsArray: [],
      },
      { status: errorStatus },
    );
  }
}
