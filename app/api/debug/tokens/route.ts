import { NextRequest, NextResponse } from 'next/server';
import { getCognitoTokenCookies, getUserEmailFromToken } from '@/lib/session';

/**
 * GET /api/debug/tokens
 * Debug endpoint to check stored Cognito tokens
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();

  console.log(`🧪 [DEBUG] Testing Cognito token retrieval...`);

  try {
    // Get tokens from cookies
    const tokens = await getCognitoTokenCookies();
    const userEmail = await getUserEmailFromToken();

    console.log(`📊 [DEBUG] Token status:`, {
      hasAccessToken: !!tokens.accessToken,
      hasIdToken: !!tokens.idToken,
      userEmail: userEmail,
      accessTokenLength: tokens.accessToken?.length || 0,
      idTokenLength: tokens.idToken?.length || 0,
    });

    const totalDuration = Date.now() - startTime;
    console.log(`✅ [DEBUG] Token test completed in ${totalDuration}ms`);

    return NextResponse.json({
      success: true,
      tokens: {
        hasAccessToken: !!tokens.accessToken,
        hasIdToken: !!tokens.idToken,
        accessTokenPreview: tokens.accessToken ? `${tokens.accessToken.substring(0, 20)}...` : null,
        idTokenPreview: tokens.idToken ? `${tokens.idToken.substring(0, 20)}...` : null,
      },
      userEmail: userEmail,
      duration: totalDuration,
    });
  } catch (error) {
    console.error(`❌ [DEBUG] Token test failed:`, error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime,
      },
      { status: 500 },
    );
  }
}

// Disable other HTTP methods
export async function POST() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
