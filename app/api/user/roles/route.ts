import { NextRequest, NextResponse } from 'next/server';
import { getSession, getCognitoTokenCookies, getCurrentSessionId } from '@/lib/session';

/**
 * Simple API Gateway for User Roles
 * Directly calls QBraid API: api.qbraid.com/orgs/users/:orgId/:page/:limit
 * Much simpler than the complex multi-step role fetching
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();

  try {
    // Get user session
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Get parameters from query params
    const { searchParams } = new URL(request.url);
    const orgId = searchParams.get('orgId');
    const email = searchParams.get('email'); // Enhanced: support email parameter for granular caching
    const page = searchParams.get('page') || '0';
    const limit = searchParams.get('limit') || '100';

    if (!orgId) {
      return NextResponse.json({ error: 'orgId parameter is required' }, { status: 400 });
    }

    // Use provided email or session email
    const targetEmail = email || session.email;

    // Get tokens for QBraid API authentication
    const sessionId = await getCurrentSessionId();
    const tokens = await getCognitoTokenCookies(sessionId || undefined);

    if (!tokens.idToken) {
      return NextResponse.json({ error: 'ID token not available' }, { status: 401 });
    }

    console.log(`🔍 [USER-ROLES] Fetching roles for user ${targetEmail} in org ${orgId}`, {
      requestedEmail: email,
      sessionEmail: session.email,
      targetEmail,
      orgId,
    });

    // Direct call to QBraid API - SIMPLE!
    const qbraidUrl = `https://api.qbraid.com/api/orgs/get/0/10`;

    const response = await fetch(qbraidUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'id-token': tokens.idToken,
        email: targetEmail, // Use target email for enhanced role caching
        // domain: 'qbraid',
      },
    });

    if (!response.ok) {
      console.error(`❌ [USER-ROLES] QBraid API error: ${response.status}`);
      return NextResponse.json(
        { error: `QBraid API error: ${response.status}` },
        { status: response.status },
      );
    }

    const data = await response.json();

    // Find target user in the org users list
    const currentUser = data.orgUsers?.find(
      (user: any) => user.email?.toLowerCase() === targetEmail.toLowerCase(),
    );

    const userRole = currentUser?.role || null;
    const totalTime = Date.now() - startTime;

    console.log(`✅ [USER-ROLES] Found role "${userRole}" for ${targetEmail} in ${totalTime}ms`, {
      targetEmail,
      orgId,
      userRole,
      isEnhancedRequest: !!email,
    });

    return NextResponse.json({
      success: true,
      data: {
        email: targetEmail,
        orgId: orgId,
        role: userRole,
        currentUserRole: data.currentUserRole, // From QBraid API response
        orgName: data.orgName || 'Unknown Organization', // Enhanced: include org name
        orgUsers: data.orgUsers,
        pagination: data.pagination,
        // Enhanced: include metadata for caching
        enhanced: !!email,
        requestedEmail: email,
        sessionEmail: session.email,
      },
      processingTime: `${totalTime}ms`,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [USER-ROLES] Error:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: `${totalTime}ms`,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

/**
 * Get user roles across multiple organizations (if needed)
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { orgIds = [] } = await request.json();

    if (!Array.isArray(orgIds) || orgIds.length === 0) {
      return NextResponse.json({ error: 'orgIds array is required' }, { status: 400 });
    }

    // Get tokens
    const sessionId = await getCurrentSessionId();
    const tokens = await getCognitoTokenCookies(sessionId || undefined);

    if (!tokens.idToken) {
      return NextResponse.json({ error: 'ID token not available' }, { status: 401 });
    }

    console.log(`🔍 [USER-ROLES] Fetching roles for ${session.email} across ${orgIds.length} orgs`);

    // Simple parallel calls to QBraid API
    const rolePromises = orgIds.map(async (orgId: string) => {
      try {
        const qbraidUrl = `https://api.qbraid.com/api/orgs/users/${orgId}/0/100`;

        const response = await fetch(qbraidUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'id-token': tokens.idToken!,
            email: session.email,
            domain: 'qbraid',
          },
        });

        if (!response.ok) {
          return { orgId, role: null, error: `API error: ${response.status}` };
        }

        const data = await response.json();
        const currentUser = data.orgUsers?.find(
          (user: any) => user.email?.toLowerCase() === session.email.toLowerCase(),
        );

        return {
          orgId,
          role: currentUser?.role || null,
          currentUserRole: data.currentUserRole,
        };
      } catch (error) {
        return {
          orgId,
          role: null,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    });

    const results = await Promise.all(rolePromises);
    const roles = results
      .filter((result) => result.role && !result.error)
      .map((result) => result.role);

    const totalTime = Date.now() - startTime;

    console.log(
      `✅ [USER-ROLES] Found ${roles.length} roles for ${session.email} in ${totalTime}ms`,
    );

    return NextResponse.json({
      success: true,
      data: {
        email: session.email,
        roles: [...new Set(roles)], // Remove duplicates
        orgRoles: results,
        totalOrgsChecked: orgIds.length,
        rolesFound: roles.length,
      },
      processingTime: `${totalTime}ms`,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [USER-ROLES] Bulk fetch error:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: `${totalTime}ms`,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}
