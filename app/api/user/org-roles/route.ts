import { NextRequest, NextResponse } from 'next/server';
import { getSession, getCognitoTokenCookies, getCurrentSessionId } from '@/lib/session';
import { getUserOrgRoles, getUserRoleInOrg } from '@/lib/external-roles';

/**
 * Enhanced API Gateway for Organization-Specific User Roles
 * GET /api/user/org-roles - Get all org roles for user
 * GET /api/user/org-roles?orgId=123 - Get role for specific org
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();

  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const orgId = searchParams.get('orgId');
    const forceRefresh = searchParams.get('forceRefresh') === 'true';

    // Get tokens for QBraid API authentication
    const sessionId = await getCurrentSessionId();
    const tokens = await getCognitoTokenCookies(sessionId || undefined);

    if (!tokens.idToken) {
      return NextResponse.json({ error: 'ID token not available' }, { status: 401 });
    }

    console.log(
      `🔍 [USER-ORG-ROLES] Fetching roles for ${session.email}${orgId ? ` in org ${orgId}` : ' (all orgs)'}`,
    );

    if (orgId) {
      // Get role for specific organization
      const role = await getUserRoleInOrg(session.email, orgId, tokens.idToken, forceRefresh);

      const totalTime = Date.now() - startTime;
      console.log(`✅ [USER-ORG-ROLES] Retrieved role for org ${orgId} in ${totalTime}ms`);

      return NextResponse.json({
        success: true,
        data: {
          email: session.email,
          orgId,
          role,
          hasRole: !!role,
        },
        metadata: {
          requestDuration: totalTime,
          cached: !forceRefresh,
          timestamp: new Date().toISOString(),
        },
      });
    } else {
      // Get all organization roles
      const orgRoles = await getUserOrgRoles(session.email, forceRefresh, tokens.idToken);

      const totalTime = Date.now() - startTime;
      console.log(
        `✅ [USER-ORG-ROLES] Retrieved roles for ${Object.keys(orgRoles).length} organizations in ${totalTime}ms`,
      );

      return NextResponse.json({
        success: true,
        data: {
          email: session.email,
          organizations: orgRoles,
          organizationCount: Object.keys(orgRoles).length,
          totalRoles: Object.values(orgRoles).map((org) => org.role),
        },
        metadata: {
          requestDuration: totalTime,
          cached: !forceRefresh,
          timestamp: new Date().toISOString(),
        },
      });
    }
  } catch (error: any) {
    const totalTime = Date.now() - startTime;
    console.error(`❌ [USER-ORG-ROLES] Request failed in ${totalTime}ms:`, error);

    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to fetch organization roles',
        metadata: {
          requestDuration: totalTime,
          timestamp: new Date().toISOString(),
        },
      },
      { status: 500 },
    );
  }
}

/**
 * Update user role in specific organization (for admin operations)
 * POST /api/user/org-roles
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const body = await request.json();
    const { targetEmail, orgId, newRole, orgName } = body;

    // Validate required fields
    if (!targetEmail || !orgId || !newRole) {
      return NextResponse.json(
        { error: 'targetEmail, orgId, and newRole are required' },
        { status: 400 },
      );
    }

    // TODO: Add permission check here - verify user can update roles in this org

    console.log(
      `🔄 [USER-ORG-ROLES] Admin ${session.email} updating role for ${targetEmail} in org ${orgId}: ${newRole}`,
    );

    // Import the update function
    const { updateUserRoleInOrg } = await import('@/lib/external-roles');
    await updateUserRoleInOrg(targetEmail, orgId, newRole, orgName);

    const totalTime = Date.now() - startTime;
    console.log(`✅ [USER-ORG-ROLES] Role update completed in ${totalTime}ms`);

    return NextResponse.json({
      success: true,
      message: `Updated role for ${targetEmail} in organization ${orgId}`,
      data: {
        targetEmail,
        orgId,
        newRole,
        updatedBy: session.email,
      },
      metadata: {
        requestDuration: totalTime,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error: any) {
    const totalTime = Date.now() - startTime;
    console.error(`❌ [USER-ORG-ROLES] Role update failed in ${totalTime}ms:`, error);

    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to update organization role',
        metadata: {
          requestDuration: totalTime,
          timestamp: new Date().toISOString(),
        },
      },
      { status: 500 },
    );
  }
}
