import axios, { AxiosInstance, AxiosError } from 'axios';
import { getCognitoTokenCookies, getUserEmailFromToken, getCurrentSessionId } from '@/lib/session';

// Determine base URL based on environment - Always use QBraid API
const env = process.env.NODE_ENV;
const customEnv = process.env.NEXT_PUBLIC_NODE_ENV;
const baseURL = 'https://api.qbraid.com/api'; // Default to staging instead of localhost

// Types for API responses and errors
interface APIErrorResponse {
  message?: string;
  error?: string;
  [key: string]: unknown;
}

/**
 * Server-side HTTP client for external QBraid API
 * Handles authentication and request/response transformations
 */
class ExternalAPIClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL,
      timeout: 30000,
      headers: { 'Content-Type': 'application/json' },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor for auth with session-aware token retrieval
    this.client.interceptors.request.use(
      async (config) => {
        try {
          // Get current session ID for Redis-based token retrieval
          const sessionId = await getCurrentSessionId();
          const tokens = await getCognitoTokenCookies(sessionId || undefined);
          const userEmail = await getUserEmailFromToken();

          // Priority: ID Token → Access Token → API Key (following auth wall guide)
          if (tokens.idToken) {
            // Option 1: ID Token (Recommended)
            config.headers['id-token'] = tokens.idToken;
            if (userEmail) {
              config.headers['email'] = userEmail; // optional with ID token
            }
            config.headers['domain'] = 'qbraid'; // optional
            console.log(`🔐 [API-CLIENT] Using ID token from ${sessionId ? 'Redis' : 'cookies'}`);
          } else if (tokens.accessToken) {
            // Option 2: Access Token (Fallback)
            config.headers['access-token'] = tokens.accessToken;
            if (userEmail) {
              config.headers['email'] = userEmail; // REQUIRED with access token
            }
            config.headers['domain'] = 'qbraid'; // optional
            console.log(
              `🔐 [API-CLIENT] Using access token fallback from ${sessionId ? 'Redis' : 'cookies'}`,
            );
          } else if (process.env.QBRAID_API_TOKEN) {
            // Option 3: API Key (Server-to-server fallback)
            config.headers['api-key'] = process.env.QBRAID_API_TOKEN;
            config.headers['domain'] = 'qbraid'; // optional
            console.log('🔐 [API-CLIENT] Using API key from environment');
          }

          // Always try to include email for better security
          if (userEmail) {
            config.headers['email'] = userEmail;
          } else if (process.env.QBRAID_API_EMAIL) {
            config.headers['email'] = process.env.QBRAID_API_EMAIL;
          }

          console.log(`🔄 [External API] ${config.method?.toUpperCase()} ${config.url}`);
        } catch (error) {
          console.error('❌ [External API] Auth setup failed:', error);
        }

        return config;
      },
      (error) => Promise.reject(error),
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        console.log(
          `✅ [External API] ${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status}`,
        );
        return response;
      },
      (error: AxiosError) => {
        console.error(
          `❌ [External API] ${error.config?.method?.toUpperCase()} ${error.config?.url} - ${error.response?.status || 'Network Error'}`,
        );

        // Transform external API errors to consistent format
        const errorResponse = {
          error: true,
          message:
            (error.response?.data as APIErrorResponse)?.message ||
            error.message ||
            'External API error',
          status: error.response?.status || 500,
          data: error.response?.data,
        };

        return Promise.reject(errorResponse);
      },
    );
  }

  // Generic methods for different HTTP verbs
  async get(url: string, params?: Record<string, unknown>) {
    return this.client.get(url, { params });
  }

  async post(url: string, data?: Record<string, unknown>) {
    return this.client.post(url, data);
  }

  async put(url: string, data?: Record<string, unknown>) {
    return this.client.put(url, data);
  }

  async patch(url: string, data?: Record<string, unknown>) {
    return this.client.patch(url, data);
  }

  async delete(url: string) {
    return this.client.delete(url);
  }
}

// Export singleton instance
export const externalClient = new ExternalAPIClient();
export default externalClient;

/**
 * Helper function to get auth headers following the auth wall guide
 * Priority: ID Token → Access Token → API Key
 */
export async function getAuthHeaders(): Promise<Record<string, string>> {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  try {
    const sessionId = await getCurrentSessionId();
    const tokens = await getCognitoTokenCookies(sessionId || undefined);
    const userEmail = await getUserEmailFromToken();

    // Priority: ID Token → Access Token → API Key (following auth wall guide)
    if (tokens.idToken) {
      // Option 1: ID Token (Recommended)
      headers['id-token'] = tokens.idToken;
      if (userEmail) {
        headers['email'] = userEmail; // optional with ID token
      }
      headers['domain'] = 'qbraid'; // optional
    } else if (tokens.accessToken) {
      // Option 2: Access Token (Fallback)
      headers['access-token'] = tokens.accessToken;
      if (userEmail) {
        headers['email'] = userEmail; // REQUIRED with access token
      }
      headers['domain'] = 'qbraid'; // optional
    } else if (process.env.QBRAID_API_TOKEN) {
      // Option 3: API Key (Server-to-server fallback)
      headers['api-key'] = process.env.QBRAID_API_TOKEN;
      headers['domain'] = 'qbraid'; // optional
    }

    // Always try to include email for better security
    if (userEmail) {
      headers['email'] = userEmail;
    } else if (process.env.QBRAID_API_EMAIL) {
      headers['email'] = process.env.QBRAID_API_EMAIL;
    }

    return headers;
  } catch (error) {
    console.error('❌ [AUTH-HEADERS] Failed to get auth headers:', error);
    return headers; // Return basic headers if auth setup fails
  }
}
