import { NextRequest, NextResponse } from 'next/server';
import { getSession, getCurrentSessionId, getCognitoTokenCookies } from '@/lib/session';
import {
  processOrganizationsForRoles,
  updateSessionRoles,
  getRolesForUser,
  getCachedOrgRoles,
  UserOrgRoles,
} from '@/lib/external-roles';
import { mapRolesToPermissions } from '@/lib/permissions';
import { ExternalRoleResponse, Permission } from '@/types/auth';
import { externalClient } from '@/app/api/_utils/external-client';

// Performance tracking for 19k users
const PERFORMANCE_THRESHOLD_MS = 5000; // Log slow requests

/**
 * GET /api/auth/permissions
 *
 * Fetches user roles and permissions with optimizations for high-volume usage.
 * Architecture: Check Session Cache -> Check Role Cache -> External API -> Process -> Cache
 *
 * Query Parameters:
 * - orgId: Optional organization ID to get organization-specific permissions
 * - email: Optional email to get permissions for a specific user (admin only)
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  const requestId = Math.random().toString(36).substring(2, 15);
  const startTime = Date.now();

  // Extract query parameters
  const { searchParams } = new URL(req.url);
  const orgId = searchParams.get('orgId');
  const email = searchParams.get('email');

  console.log(`🚀 [PERMISSIONS-API] =============== STARTING ROLES FLOW ===============`);
  console.log(`🚀 [PERMISSIONS-API] Request ID: ${requestId}`);
  console.log(`🚀 [PERMISSIONS-API] Timestamp: ${new Date().toISOString()}`);
  console.log(`🚀 [PERMISSIONS-API] Query Parameters:`, {
    orgId: orgId || 'none (global permissions)',
    email: email || 'current user',
    isOrgSpecific: !!orgId,
  });
  console.log(
    `🚀 [PERMISSIONS-API] User Agent: ${req.headers.get('user-agent')?.substring(0, 100)}...`,
  );

  try {
    console.log(`🔍 [PERMISSIONS-API] Step 1: Verifying user session...`);

    // 1. Verify user session
    const sessionStart = Date.now();
    const session = await getSession();
    const sessionTime = Date.now() - sessionStart;

    console.log(`📋 [PERMISSIONS-API] Session retrieval took ${sessionTime}ms`);
    console.log(`📋 [PERMISSIONS-API] Session data:`, {
      hasSession: !!session,
      email: session?.email || 'none',
      userId: session?.userId || 'none',
      externalRolesCount: session?.externalRoles?.length || 0,
      permissionsCount: session?.permissions?.length || 0,
      signedIn: session?.signedIn || false,
      sessionId: session?.jti?.substring(0, 10) + '...' || 'none',
    });

    if (!session?.email) {
      console.log(`❌ [PERMISSIONS-API] FAILURE: No valid session found`, { requestId });
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userEmail = session.email;
    console.log(`👤 [PERMISSIONS-API] SUCCESS: User authenticated as: ${userEmail}`, { requestId });

    // 2. Get session ID for token retrieval and caching
    console.log(`🔍 [PERMISSIONS-API] Step 2: Getting session ID for caching...`);
    const sessionIdStart = Date.now();
    const sessionId = await getCurrentSessionId();
    const sessionIdTime = Date.now() - sessionIdStart;

    console.log(`📋 [PERMISSIONS-API] Session ID retrieval took ${sessionIdTime}ms`);
    console.log(
      `📋 [PERMISSIONS-API] Session ID: ${sessionId?.substring(0, 10)}... (length: ${sessionId?.length || 0})`,
    );

    if (!sessionId) {
      console.log(`❌ [PERMISSIONS-API] FAILURE: No session ID found`, { requestId });
      return NextResponse.json({ error: 'No session ID' }, { status: 401 });
    }

    console.log(`🔍 [PERMISSIONS-API] No roles in session, proceeding to cache checks...`);

    // 4. Check email-based org roles cache first (preferred method)
    console.log(`🔍 [PERMISSIONS-API] Step 4a: Checking email-based org roles cache...`);
    console.log(`🔍 [PERMISSIONS-API] Cache key pattern: orgroles:${userEmail}`);

    const orgCacheStart = Date.now();
    const cachedOrgRoles = await getCachedOrgRoles(userEmail);
    const orgCacheTime = Date.now() - orgCacheStart;

    console.log(`📋 [PERMISSIONS-API] Org roles cache check took ${orgCacheTime}ms`);
    console.log(`📋 [PERMISSIONS-API] Org cache result:`, {
      hasOrgRoles: !!cachedOrgRoles,
      orgCount: cachedOrgRoles ? Object.keys(cachedOrgRoles).length : 0,
      orgRoles: cachedOrgRoles || {},
    });

    // If we got cached org roles, convert to simple roles and use them
    if (cachedOrgRoles && Object.keys(cachedOrgRoles).length > 0) {
      // Handle organization-specific vs global permissions
      let simpleRoles: string[];
      let permissionScope: string;

      if (orgId && cachedOrgRoles[orgId]) {
        // Organization-specific permissions: only return role for the specified org
        simpleRoles = [cachedOrgRoles[orgId].role];
        permissionScope = `org-specific (${cachedOrgRoles[orgId].orgName})`;
      } else {
        // Global permissions: return all roles across all organizations
        simpleRoles = Object.values(cachedOrgRoles).map((org: UserOrgRoles[string]) => org.role);
        permissionScope = 'global (all organizations)';
      }

      console.log(`⚡ [PERMISSIONS-API] SUCCESS: Found org roles in email-based cache!`, {
        requestId,
        orgCount: Object.keys(cachedOrgRoles).length,
        userEmail: userEmail,
        requestedOrgId: orgId || 'none',
        permissionScope,
        simpleRoles: simpleRoles,
        detailedOrgRoles: cachedOrgRoles,
      });

      const permissionStart = Date.now();
      const permissions = mapRolesToPermissions(simpleRoles);
      const permissionTime = Date.now() - permissionStart;

      console.log(`🎭 [PERMISSIONS-API] Permission mapping took ${permissionTime}ms`);
      console.log(
        `🎭 [PERMISSIONS-API] Mapped ${simpleRoles.length} roles to ${permissions.length} permissions`,
      );

      const duration = Date.now() - startTime;
      console.log(
        `✅ [PERMISSIONS-API] =============== COMPLETED (EMAIL CACHE) in ${duration}ms ===============`,
      );

      return NextResponse.json({
        roles: simpleRoles,
        permissions,
        source: 'email_cache',
        cached: true,
        duration: `${duration}ms`,
        orgRoles: cachedOrgRoles, // Include detailed org info
        // Organization context information
        orgContext: {
          requestedOrgId: orgId,
          isOrgSpecific: !!orgId,
          scope: permissionScope,
          currentOrg: orgId && cachedOrgRoles[orgId] ? cachedOrgRoles[orgId] : null,
        },
      });
    }

    console.log(`🔍 [PERMISSIONS-API] No org roles in email cache, checking session cache...`);

    // 4b. Check session-based role cache as fallback
    console.log(`🔍 [PERMISSIONS-API] Step 4b: Checking session-based role cache...`);
    console.log(
      `🔍 [PERMISSIONS-API] Cache key pattern: session-roles:${sessionId.substring(0, 10)}...`,
    );

    const cacheStart = Date.now();
    const cachedRoles = await getRolesForUser(userEmail, false, undefined, sessionId);
    const cacheTime = Date.now() - cacheStart;

    console.log(`📋 [PERMISSIONS-API] Redis cache check took ${cacheTime}ms`);
    console.log(`📋 [PERMISSIONS-API] Cache result:`, {
      hasRoles: !!(cachedRoles && cachedRoles.length > 0),
      rolesCount: cachedRoles?.length || 0,
      roles: cachedRoles || [],
    });

    // If we got cached roles, use them
    if (cachedRoles && cachedRoles.length > 0) {
      console.log(`⚡ [PERMISSIONS-API] SUCCESS: Found roles in Redis cache!`, {
        requestId,
        rolesCount: cachedRoles.length,
        sessionId: sessionId.substring(0, 10) + '...',
        roles: cachedRoles,
      });

      const permissionStart = Date.now();
      const permissions = mapRolesToPermissions(cachedRoles);
      const permissionTime = Date.now() - permissionStart;

      console.log(`🎭 [PERMISSIONS-API] Permission mapping took ${permissionTime}ms`);
      console.log(
        `🎭 [PERMISSIONS-API] Mapped ${cachedRoles.length} roles to ${permissions.length} permissions`,
      );

      const duration = Date.now() - startTime;
      console.log(
        `✅ [PERMISSIONS-API] =============== COMPLETED (REDIS CACHE) in ${duration}ms ===============`,
      );

      return NextResponse.json({
        roles: cachedRoles,
        permissions,
        source: 'redis_cache',
        cached: true,
        duration: `${duration}ms`,
      });
    }

    console.log(`🔍 [PERMISSIONS-API] No cached roles found, proceeding to external API...`);

    // 5. Get Cognito tokens for external API calls
    console.log(`🔍 [PERMISSIONS-API] Step 5: Getting Cognito tokens...`);
    const tokenStart = Date.now();
    const tokens = await getCognitoTokenCookies(sessionId);
    const tokenTime = Date.now() - tokenStart;

    console.log(`📋 [PERMISSIONS-API] Token retrieval took ${tokenTime}ms`);
    console.log(`📋 [PERMISSIONS-API] Token info:`, {
      hasAccessToken: !!tokens?.accessToken,
      hasIdToken: !!tokens?.idToken,
      accessTokenLength: tokens?.accessToken?.length || 0,
      idTokenLength: tokens?.idToken?.length || 0,
      idTokenPrefix: tokens?.idToken?.substring(0, 20) + '...' || 'none',
    });

    if (!tokens?.idToken) {
      console.log(`❌ [PERMISSIONS-API] FAILURE: No ID token found for session`, {
        requestId,
        sessionId: sessionId.substring(0, 10),
      });
      return NextResponse.json({ error: 'No authentication token' }, { status: 401 });
    }

    // 6. Fetch organizations directly from external QBraid API
    console.log(`🔍 [PERMISSIONS-API] Step 6: Calling external QBraid API...`);
    console.log(`📤 [PERMISSIONS-API] API Details:`, {
      endpoint: '/orgs/get/0/50',
      userEmail: userEmail,
      authMethod: 'Bearer Token',
      tokenPrefix: tokens.idToken.substring(0, 20) + '...',
    });

    // Fetch organizations list
    const apiStart = Date.now();
    const organizationsResponse = await externalClient.get('/orgs/get/0/50', {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    const apiTime = Date.now() - apiStart;

    console.log(`📋 [PERMISSIONS-API] External API call took ${apiTime}ms`);
    console.log(`📋 [PERMISSIONS-API] API Response:`, {
      status: organizationsResponse.status,
      statusText: organizationsResponse.statusText,
      hasData: !!organizationsResponse.data,
      dataSize: JSON.stringify(organizationsResponse.data || {}).length,
    });

    if (organizationsResponse.status !== 200) {
      console.error(`❌ [PERMISSIONS-API] FAILURE: Organizations API failed:`, {
        status: organizationsResponse.status,
        statusText: organizationsResponse.statusText,
        requestId,
        responseData: organizationsResponse.data,
      });

      // Return empty but successful response to allow user to continue
      const permissions: Permission[] = [];
      const duration = Date.now() - startTime;
      console.log(
        `⚠️ [PERMISSIONS-API] =============== COMPLETED (API ERROR FALLBACK) in ${duration}ms ===============`,
      );

      return NextResponse.json({
        roles: [],
        permissions,
        source: 'api_error_fallback',
        cached: false,
        error: 'Failed to fetch organizations',
        duration: `${duration}ms`,
      });
    }

    const organizationsData = organizationsResponse.data;
    console.log(`📋 [PERMISSIONS-API] Organizations data received:`, {
      organizationsCount: organizationsData?.organizations?.length || 0,
      totalOrganizations: organizationsData?.pagination?.totalOrganizations || 0,
      currentPage: organizationsData?.pagination?.currentPage || 0,
      sampleOrgData: organizationsData?.organizations?.[0]
        ? {
            hasOrg: !!organizationsData.organizations[0].org,
            orgId:
              organizationsData.organizations[0].org?.organization?._id?.substring(0, 10) + '...' ||
              'none',
            orgName: organizationsData.organizations[0].org?.organization?.name || 'none',
            userRole: organizationsData.organizations[0].org?.role || 'none',
            userEmail: organizationsData.organizations[0].org?.email || 'none',
          }
        : null,
    });

    // 7. Process organizations data to extract user roles
    console.log(`🔍 [PERMISSIONS-API] Step 7: Processing organizations to extract roles...`);
    const rolesTimer = Date.now();
    const roles = await processOrganizationsForRoles(userEmail, organizationsData);
    const rolesFetchDuration = Date.now() - rolesTimer;

    console.log(`📥 [PERMISSIONS-API] Role processing completed in ${rolesFetchDuration}ms`);
    console.log(`📥 [PERMISSIONS-API] Processed roles:`, {
      email: userEmail,
      rolesCount: roles.length,
      roles: roles,
      processingDuration: `${rolesFetchDuration}ms`,
    });

    // 8. Cache roles using session ID for future requests
    console.log(`🔍 [PERMISSIONS-API] Step 8: Caching roles for future requests...`);
    if (sessionId && roles.length > 0) {
      try {
        const cacheStart = Date.now();
        await updateSessionRoles(sessionId, roles, userEmail);
        const cacheTime = Date.now() - cacheStart;
        console.log(`💾 [PERMISSIONS-API] Role caching completed in ${cacheTime}ms`);
      } catch (cacheError) {
        console.warn(`⚠️ [PERMISSIONS-API] Failed to cache roles for session, but continuing:`, {
          error: cacheError,
          sessionId: sessionId.substring(0, 10) + '...',
          rolesCount: roles.length,
        });
      }
    } else {
      console.log(`⚠️ [PERMISSIONS-API] Skipping role caching:`, {
        hasSessionId: !!sessionId,
        rolesCount: roles.length,
        reason: !sessionId ? 'No session ID' : 'No roles to cache',
      });
    }

    // 9. Map roles to permissions
    console.log(`🔍 [PERMISSIONS-API] Step 9: Mapping roles to permissions...`);
    const permissionStart = Date.now();
    const permissions = mapRolesToPermissions(roles);
    const permissionTime = Date.now() - permissionStart;

    console.log(`🎭 [PERMISSIONS-API] Permission mapping completed in ${permissionTime}ms`);
    console.log(`🎭 [PERMISSIONS-API] Permission mapping result:`, {
      inputRoles: roles,
      outputPermissions: permissions,
      permissionCount: permissions.length,
      mappingDetails: roles.map((role) => ({
        role,
        permissions: mapRolesToPermissions([role]),
      })),
    });

    // 10. Track performance for monitoring
    const totalDuration = Date.now() - startTime;
    console.log(`📊 [PERMISSIONS-API] Performance metrics:`, {
      sessionRetrievalTime: `${sessionTime}ms`,
      sessionIdTime: `${sessionIdTime}ms`,
      cacheCheckTime: `${cacheTime}ms`,
      tokenRetrievalTime: `${tokenTime}ms`,
      externalApiTime: `${apiTime}ms`,
      roleProcessingTime: `${rolesFetchDuration}ms`,
      permissionMappingTime: `${permissionTime}ms`,
      totalDuration: `${totalDuration}ms`,
    });

    if (totalDuration > PERFORMANCE_THRESHOLD_MS) {
      console.warn(`⚠️ [PERMISSIONS-API] SLOW REQUEST DETECTED:`, {
        requestId,
        userEmail,
        duration: `${totalDuration}ms`,
        threshold: `${PERFORMANCE_THRESHOLD_MS}ms`,
        breakdown: {
          sessionTime: `${sessionTime}ms`,
          cacheTime: `${cacheTime}ms`,
          apiTime: `${apiTime}ms`,
          processingTime: `${rolesFetchDuration}ms`,
        },
      });
    }

    console.log(`🎉 [PERMISSIONS-API] SUCCESS: All steps completed successfully!`);
    console.log(`📊 [PERMISSIONS-API] Final summary:`, {
      rolesCount: roles.length,
      permissionsCount: permissions.length,
      source: roles.length > 0 ? 'external_api' : 'empty',
      totalDuration: `${totalDuration}ms`,
      requestId,
    });

    console.log(
      `✅ [PERMISSIONS-API] =============== COMPLETED (EXTERNAL API) in ${totalDuration}ms ===============`,
    );

    // 11. Return successful response
    return NextResponse.json({
      roles,
      permissions,
      source: 'external_api',
      cached: false,
      duration: `${totalDuration}ms`,
    });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(
      `❌ [PERMISSIONS-API] =============== CRITICAL ERROR AFTER ${duration}ms ===============`,
    );
    console.error(`❌ [PERMISSIONS-API] Error details:`, {
      requestId,
      errorMessage: error.message,
      errorStack: error.stack,
      errorName: error.name,
      errorCause: error.cause,
      duration: `${duration}ms`,
    });

    // Return error response but allow frontend to continue with empty permissions
    return NextResponse.json(
      {
        error: 'Failed to fetch permissions',
        message: error.message,
        roles: [],
        permissions: [],
        source: 'error',
        duration: `${duration}ms`,
      },
      { status: 500 },
    );
  }
}
