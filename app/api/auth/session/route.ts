import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/session';

/**
 * GET /api/auth/session
 * Returns current user session information
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    
    if (!session) {
      return NextResponse.json({ error: 'No active session' }, { status: 401 });
    }

    return NextResponse.json({
      email: session.email,
      sessionId: session.sessionId,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ [SESSION-API] Error getting session:', error);
    return NextResponse.json(
      { error: 'Failed to get session' },
      { status: 500 }
    );
  }
}
