import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';
import type { JobsRowProps } from '@/types/jobs';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const provider = searchParams.get('provider');
    const qbraidDeviceId = searchParams.get('qbraidDeviceId');
    const page = searchParams.get('page') || '0';
    const resultsPerPage = searchParams.get('resultsPerPage') || '10';

    if (!provider || !qbraidDeviceId) {
      return NextResponse.json(
        {
          error: 'Provider and device ID are required',
        },
        { status: 400 },
      );
    }

    // Build query params
    const params = new URLSearchParams({
      provider,
      qbraidDeviceId,
      page,
      resultsPerPage,
    });

    // Proxy to external API
    const response = await externalClient.get(`/quantum-jobs/all-by-provider?${params.toString()}`);
    let data = response.data;

    // Handle empty results
    if (!data.jobsArray || data.jobsArray.length === 0) {
      data.jobsArray = [{ qbraidDeviceId: 'No jobs found' } as unknown as JobsRowProps];
    }

    // Transform timestamps
    data.jobsArray = data.jobsArray.map((job: JobsRowProps) => {
      if (job.timeStamps?.createdAt && typeof job.timeStamps.createdAt === 'string') {
        job.timeStamps.createdAt = new Date(job.timeStamps.createdAt);
      }
      if (job.timeStamps?.endedAt && typeof job.timeStamps.endedAt === 'string') {
        job.timeStamps.endedAt = new Date(job.timeStamps.endedAt);
      }
      return job;
    });

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ [API Gateway] Error fetching quantum jobs:', error);

    return NextResponse.json(
      {
        error: error.message || 'Failed to fetch quantum jobs',
        jobsArray: [{ qbraidDeviceId: 'No jobs found' } as unknown as JobsRowProps],
        total: 0,
      },
      { status: error.status || 500 },
    );
  }
}
