import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    if (!body || !body.email || !body.orgId || !body.role) {
      return NextResponse.json(
        {
          error: 'Email, organization ID, and role are required',
        },
        { status: 400 },
      );
    }

    // Proxy to external API
    await externalClient.post('/orgs/users/update', body);

    return NextResponse.json({
      success: true,
      message: 'User role updated successfully',
    });
  } catch (error: any) {
    console.error('❌ [API Gateway] Error updating user role:', error);

    return NextResponse.json(
      {
        error: error.message || 'Failed to update user role',
        success: false,
      },
      { status: error.status || 500 },
    );
  }
}
