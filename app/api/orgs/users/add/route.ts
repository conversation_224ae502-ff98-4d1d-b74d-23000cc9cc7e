import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    if (!body || !body.email || !body.orgId) {
      return NextResponse.json(
        {
          error: 'Email and organization ID are required',
        },
        { status: 400 },
      );
    }

    // Proxy to external API
    await externalClient.post('/orgs/users/add', body);

    return NextResponse.json({
      success: true,
      message: 'User invited successfully',
    });
  } catch (error: any) {
    console.error('❌ [API Gateway] Error inviting user:', error);

    return NextResponse.json(
      {
        error: error.message || 'Failed to invite user',
        success: false,
      },
      { status: error.status || 500 },
    );
  }
}
