import { NextRequest, NextResponse } from 'next/server';
import { getSession, getCognitoTokenCookies, getCurrentSessionId } from '@/lib/session';
import { externalClient } from '@/app/api/_utils/external-client';

/**
 * API Gateway for Organization Users
 * GET /api/orgs/users/:orgId/:page/:limit - Get users in organization
 * This endpoint is used by lib/external-roles.ts to fetch user roles per organization
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ params: string[] }> },
) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(2, 15);

  try {
    const resolvedParams = await params;
    const [orgId, page = '0', limit = '100'] = resolvedParams.params;

    console.log('🚀 [ORGS-USERS-API] Starting organization users fetch:', {
      requestId,
      orgId,
      page,
      limit,
      timestamp: new Date().toISOString(),
    });

    // Validate required parameters
    if (!orgId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    // Get session for authentication
    const session = await getSession();
    if (!session) {
      console.warn('⚠️ [ORGS-USERS-API] No valid session found');
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Get tokens for external API call
    const sessionId = await getCurrentSessionId();
    const tokens = await getCognitoTokenCookies(sessionId || undefined);

    if (!tokens.idToken) {
      console.warn('⚠️ [ORGS-USERS-API] No ID token available');
      return NextResponse.json({ error: 'Authentication token unavailable' }, { status: 401 });
    }

    console.log(`📤 [ORGS-USERS-API] Making external API call for org ${orgId}...`);
    const externalApiStart = Date.now();

    // Call external API through our client
    const response = await externalClient.get(`/orgs/users/${orgId}/${page}/${limit}`);

    const externalApiTime = Date.now() - externalApiStart;
    const totalTime = Date.now() - startTime;

    console.log('📥 [ORGS-USERS-API] External API response received:', {
      requestId,
      orgId,
      responseSize: JSON.stringify(response.data).length,
      orgUsersCount: response.data?.orgUsers?.length || 0,
      apiCallDuration: externalApiTime + 'ms',
      totalDuration: totalTime + 'ms',
    });

    // Return the response with metadata
    return NextResponse.json({
      ...response.data,
      metadata: {
        requestId,
        orgId,
        page: parseInt(page),
        limit: parseInt(limit),
        requestDuration: totalTime,
        apiCallDuration: externalApiTime,
        timestamp: new Date().toISOString(),
        userEmail: session.email,
      },
    });
  } catch (error: any) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [ORGS-USERS-API] Request failed:', {
      requestId,
      error: error.message,
      status: error.status,
      duration: totalTime + 'ms',
    });

    return NextResponse.json(
      {
        error: error.message || 'Failed to fetch organization users',
        orgUsers: [],
        metadata: {
          requestId,
          requestDuration: totalTime,
          timestamp: new Date().toISOString(),
          failed: true,
        },
      },
      { status: error.status || 500 },
    );
  }
}
