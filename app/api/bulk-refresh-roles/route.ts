import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/session';
import { getRolesForUser, invalidateUserRoles } from '@/lib/external-roles';

/**
 * Bulk role refresh API endpoint for high-volume scenarios
 * Optimized for refreshing roles for multiple users efficiently
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(2, 15);

  console.log('🔄 [BULK-REFRESH] Starting bulk role refresh...', { requestId });

  try {
    // Verify admin access for bulk operations
    const session = await getSession();
    if (!session) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
          requestId,
        },
        { status: 401 },
      );
    }

    // Parse request body
    const body = await request.json().catch(() => ({}));
    const { emails = [], forceRefresh = true, batchSize = 10 } = body;

    // Validate input
    if (!Array.isArray(emails) || emails.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'emails array is required and cannot be empty',
          requestId,
        },
        { status: 400 },
      );
    }

    if (emails.length > 100) {
      return NextResponse.json(
        {
          success: false,
          error: 'Maximum 100 emails per batch',
          requestId,
        },
        { status: 400 },
      );
    }

    console.log(`📊 [BULK-REFRESH] Processing ${emails.length} users in batches of ${batchSize}`, {
      requestId,
      totalUsers: emails.length,
      batchSize,
    });

    const results = [];
    const errors = [];

    // Process emails in batches to prevent overwhelming the API
    for (let i = 0; i < emails.length; i += batchSize) {
      const batch = emails.slice(i, i + batchSize);
      console.log(
        `🔄 [BULK-REFRESH] Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(emails.length / batchSize)}`,
        {
          requestId,
          batchEmails: batch,
        },
      );

      // Process batch in parallel with rate limiting
      const batchPromises = batch.map(async (email: string, index: number) => {
        try {
          // Add small delay between requests to prevent rate limiting
          await new Promise((resolve) => setTimeout(resolve, index * 50));

          if (forceRefresh) {
            await invalidateUserRoles(email);
          }

          // Note: We don't have ID tokens for other users, so this is admin-level refresh
          // In production, you might have a different API endpoint or admin tokens
          const roles = await getRolesForUser(email, forceRefresh, ''); // Empty token for admin refresh

          return {
            email,
            success: true,
            roles,
            rolesCount: roles.length,
          };
        } catch (error) {
          console.error(`❌ [BULK-REFRESH] Failed to refresh roles for ${email}:`, error);
          return {
            email,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          };
        }
      });

      const batchResults = await Promise.allSettled(batchPromises);

      // Process batch results
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          if (result.value.success) {
            results.push(result.value);
          } else {
            errors.push(result.value);
          }
        } else {
          errors.push({
            email: batch[index],
            success: false,
            error: result.reason?.message || 'Promise rejection',
          });
        }
      });

      // Small delay between batches
      if (i + batchSize < emails.length) {
        await new Promise((resolve) => setTimeout(resolve, 200));
      }
    }

    const totalTime = Date.now() - startTime;
    const successCount = results.length;
    const errorCount = errors.length;

    console.log(`🎉 [BULK-REFRESH] Bulk refresh completed`, {
      requestId,
      totalUsers: emails.length,
      successCount,
      errorCount,
      totalDuration: totalTime + 'ms',
    });

    const response = {
      success: true,
      message: `Processed ${emails.length} users: ${successCount} successful, ${errorCount} failed`,
      data: {
        totalUsers: emails.length,
        successCount,
        errorCount,
        results,
        errors,
        processingTime: totalTime + 'ms',
      },
      requestId,
      timestamp: new Date().toISOString(),
    };

    return NextResponse.json(response);
  } catch (error) {
    const totalTime = Date.now() - startTime;
    console.error('❌ [BULK-REFRESH] Bulk refresh failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        requestId,
        processingTime: totalTime + 'ms',
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

export async function GET() {
  return NextResponse.json({
    service: 'Bulk Role Refresh API',
    description: 'POST to refresh roles for multiple users in batches',
    methods: ['POST'],
    parameters: {
      emails: 'string[] - Array of user emails to refresh (max 100)',
      forceRefresh: 'boolean (optional) - Force cache invalidation before refresh',
      batchSize: 'number (optional) - Batch size for processing (default: 10, max: 50)',
    },
    limits: {
      maxEmailsPerRequest: 100,
      maxBatchSize: 50,
      rateLimitDelay: '50ms between users, 200ms between batches',
    },
    timestamp: new Date().toISOString(),
  });
}
