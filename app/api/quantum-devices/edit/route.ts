import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';

export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const deviceId = searchParams.get('id');
    const body = await request.json();

    if (!deviceId) {
      return NextResponse.json(
        {
          error: 'Device ID is required',
        },
        { status: 400 },
      );
    }

    // Proxy to external API
    const response = await externalClient.put(`/quantum-devices/edit?id=${deviceId}`, body);

    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error('❌ [API Gateway] Error updating quantum device:', error);

    return NextResponse.json(
      {
        error: error.message || 'Failed to update quantum device',
      },
      { status: error.status || 500 },
    );
  }
}
