'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { configureAmplify } from '@/lib/amplify-config';
import { handleOAuthCallback } from '@/app/(auth)/actions';

export default function OAuthCallbackPage() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [error, setError] = useState<string>('');
  const router = useRouter();

  useEffect(() => {
    const processCallback = async () => {
      try {
        // Ensure Amplify is configured
        configureAmplify();

        console.log('🔄 [AUTH] Processing OAuth callback...');

        // Wait a bit for tokens to be available after redirect
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Use the proper server action to handle OAuth callback
        const result = await handleOAuthCallback();

        if (!result.success) {
          throw new Error(result.error || 'OAuth authentication failed');
        }

        console.log('✅ [AUTH] OAuth callback processed successfully');
        setStatus('success');

        // Redirect to dashboard or specified redirect URL
        setTimeout(() => {
          const redirectTo = result.redirectTo || '/';
          router.push(redirectTo);
          router.refresh();
        }, 1000);
      } catch (err) {
        console.error('OAuth callback error:', err);
        setStatus('error');
        setError(err instanceof Error ? err.message : 'Failed to process authentication');
      }
    };

    processCallback();
  }, [router]);

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-purple-500/30 border-t-purple-500 rounded-full animate-spin mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-white mb-2">Completing sign-in...</h2>
          <p className="text-slate-400">Please wait while we authenticate your account</p>
        </div>
      </div>
    );
  }

  if (status === 'success') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-8 h-8 text-green-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-white mb-2">Sign-in successful!</h2>
          <p className="text-slate-400">Redirecting to your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] flex items-center justify-center">
      <div className="max-w-md w-full mx-auto p-6">
        <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-6 text-center">
          <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-8 h-8 text-red-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-white mb-2">Authentication failed</h2>
          <p className="text-red-400 mb-6">{error}</p>
          <button
            onClick={() => router.push('/signin')}
            className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Try again
          </button>
        </div>
      </div>
    </div>
  );
}
