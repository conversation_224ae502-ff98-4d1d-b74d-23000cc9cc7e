import { AuthProvider } from '@/components/auth/auth-provider';
import { Inter } from 'next/font/google';

const inter = Inter({ subsets: ['latin'] });

export default function AuthLayout({ children }: { children: React.ReactNode }) {
  return (
    <AuthProvider>
      <div className={`${inter.className} min-h-screen bg-[#18141f]`}>
        {/* Main Auth Content - Full height with centering */}
        <main className="min-h-[calc(100vh-12rem)] flex items-center justify-center px-4 py-8">
          <div className="w-full max-w-md">{children}</div>
        </main>
      </div>
    </AuthProvider>
  );
}
