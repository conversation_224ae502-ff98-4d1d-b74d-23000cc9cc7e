import { Suspense } from 'react';
import { ResetPasswordForm } from '@/components/auth/reset-password-form';

function ResetPasswordFormWithSuspense() {
  return (
    <Suspense
      fallback={
        <div className="w-full max-w-md h-96 bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] rounded-3xl shadow-2xl border border-purple-800/20 backdrop-blur-sm animate-pulse"></div>
      }
    >
      <ResetPasswordForm />
    </Suspense>
  );
}

export default function ResetPasswordPage() {
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <ResetPasswordFormWithSuspense />
      </div>
    </div>
  );
}
